"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// 临时在当前模块增加编辑器内的模块为搜索路径，为了能够正常 require 到 cc 模块，后续版本将优化调用方式
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() {
}
;
function unload() {
}
;
exports.methods = {
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    async saveToPrefab(component_uuid, nodeName, prefabUuid) {
        // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
        const scene = cc_1.director.getScene();
        const target = scene.getChildByPath(`Canvas/${nodeName}`);
        if (!target) {
            console.error("node not found:", nodeName);
            return;
        }
        cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
        // const json = cce.Utils.serialize(target);
        // console.log('Prefab JSON:', json);
        // Editor.Message.request('asset-db', 'save-asset', prefabUuid, json);
    },
    async saveAll() {
        var _a;
        let canvasNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByName('Canvas');
        if (!canvasNode) {
            console.error("Canvas node not found");
            return;
        }
        canvasNode.children.forEach((node) => {
            const emitter = node.getComponent('Emitter');
            if (emitter) {
                cce.Prefab.createPrefabAssetFromNode(node.uuid, `db://assets/resources/game/prefabs/emitter/${node.name}.prefab`);
            }
        });
    },
    async createNewEmitter(prefabName, prefabPath) {
        const scene = cc_1.director.getScene();
        const target = scene.getChildByName('Canvas');
        if (!target) {
            console.error("Canvas node not found");
            return;
        }
        let emitterNode = new cc_1.Node(prefabName);
        emitterNode.parent = target;
        emitterNode.setPosition(new cc_1.Vec3(0, 0, 0));
        emitterNode.addComponent('cc.UITransform'); // Ensure it has a transform component
        emitterNode.addComponent('Emitter');
        const nodeUuid = emitterNode.uuid;
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
    },
    replay(component_uuid) {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-component-method', {
            uuid: component_uuid,
            name: 'replay',
            args: []
        });
    },
    movePlayer(direction) {
        const scene = cc_1.director.getScene();
        const target = scene.getChildByName('Canvas');
        if (!target) {
            console.error("Canvas node not found");
            return;
        }
        const playerNode = target.getChildByName('Player');
        if (!playerNode) {
            console.error("Player node not found");
            return;
        }
        playerNode.setPosition(playerNode.position.add(direction));
    },
    movePlayerUp() {
        this.movePlayer(new cc_1.Vec3(0, 10, 0));
    },
    movePlayerDown() {
        this.movePlayer(new cc_1.Vec3(0, -10, 0));
    },
    movePlayerLeft() {
        this.movePlayer(new cc_1.Vec3(-10, 0, 0));
    },
    movePlayerRight() {
        this.movePlayer(new cc_1.Vec3(10, 0, 0));
    }
};
//# sourceMappingURL=data:application/json;base64,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