import { _decorator, Component, Node, CCInteger, CCFloat, Enum, Button, Label } from 'cc';
import { PathData, eSamplingStrategy } from './PathData';
const { ccclass, property } = _decorator;

/**
 * 路径采样调试助手
 * 用于测试和比较不同的采样策略效果
 */
@ccclass('PathSamplingDebugHelper')
export class PathSamplingDebugHelper extends Component {

    @property({ type: PathData, displayName: "测试路径数据" })
    public testPathData: PathData | null = null;

    @property({ type: Enum(eSamplingStrategy), displayName: "当前采样策略" })
    public currentStrategy: eSamplingStrategy = eSamplingStrategy.UniformDistance;

    @property({ type: CCInteger, displayName: "目标采样距离", tooltip: "仅对均匀距离采样有效" })
    public targetSamplingDistance: number = 20;

    @property({ type: CCFloat, displayName: "速度平滑系数", range: [0, 1], slide: true })
    public speedSmoothingFactor: number = 0.3;

    @property({ type: Button, displayName: "测试当前策略" })
    public testButton: Button | null = null;

    @property({ type: Button, displayName: "比较所有策略" })
    public compareButton: Button | null = null;

    @property({ type: Label, displayName: "结果显示" })
    public resultLabel: Label | null = null;

    onLoad() {
        if (this.testButton) {
            this.testButton.node.on(Button.EventType.CLICK, this.testCurrentStrategy, this);
        }
        if (this.compareButton) {
            this.compareButton.node.on(Button.EventType.CLICK, this.compareAllStrategies, this);
        }
    }

    /**
     * 测试当前采样策略
     */
    public testCurrentStrategy() {
        if (!this.testPathData) {
            this.logResult("错误：未设置测试路径数据");
            return;
        }

        // 备份原始设置
        const originalStrategy = this.testPathData.samplingStrategy;

        // 应用测试设置
        this.testPathData.samplingStrategy = this.currentStrategy;

        // 生成采样点
        const startTime = performance.now();
        const sampledPoints = this.testPathData.getSubdividedPoints(true);
        const endTime = performance.now();

        // 分析结果
        const analysis = this.analyzePathSampling(sampledPoints);
        const processingTime = endTime - startTime;

        // 恢复原始设置
        this.testPathData.samplingStrategy = originalStrategy;

        // 显示结果
        const strategyName = this.getStrategyName(this.currentStrategy);
        const result = `
策略: ${strategyName}
处理时间: ${processingTime.toFixed(2)}ms
采样点数: ${analysis.totalPoints}
平均距离: ${analysis.avgDistance.toFixed(2)}px
距离标准差: ${analysis.distanceStdDev.toFixed(2)}px
最大距离: ${analysis.maxDistance.toFixed(2)}px
最小距离: ${analysis.minDistance.toFixed(2)}px
速度变化: ${analysis.speedVariation.toFixed(2)}
        `;

        this.logResult(result);
    }

    /**
     * 比较所有采样策略
     */
    public compareAllStrategies() {
        if (!this.testPathData) {
            this.logResult("错误：未设置测试路径数据");
            return;
        }

        const strategies = [eSamplingStrategy.UniformDistance, eSamplingStrategy.AdaptiveSubdivision];
        const results: string[] = [];

        // 备份原始设置
        const originalStrategy = this.testPathData.samplingStrategy;

        for (const strategy of strategies) {
            this.testPathData.samplingStrategy = strategy;

            const startTime = performance.now();
            const sampledPoints = this.testPathData.getSubdividedPoints(true);
            const endTime = performance.now();

            const analysis = this.analyzePathSampling(sampledPoints);
            const processingTime = endTime - startTime;

            const strategyName = this.getStrategyName(strategy);
            results.push(`
${strategyName}:
  时间: ${processingTime.toFixed(2)}ms
  点数: ${analysis.totalPoints}
  平均距离: ${analysis.avgDistance.toFixed(2)}px
  距离标准差: ${analysis.distanceStdDev.toFixed(2)}px
  速度变化: ${analysis.speedVariation.toFixed(2)}
            `);
        }

        // 恢复原始设置
        this.testPathData.samplingStrategy = originalStrategy;

        // 显示比较结果
        const compareResult = "=== 采样策略比较 ===" + results.join("\n");
        this.logResult(compareResult);
    }

    /**
     * 分析路径采样质量
     */
    private analyzePathSampling(points: any[]): any {
        if (points.length < 2) {
            return {
                totalPoints: points.length,
                avgDistance: 0,
                distanceStdDev: 0,
                maxDistance: 0,
                minDistance: 0,
                speedVariation: 0
            };
        }

        // 计算相邻点距离
        const distances: number[] = [];
        const speeds: number[] = [];

        for (let i = 1; i < points.length; i++) {
            const dist = Math.sqrt(
                Math.pow(points[i].x - points[i-1].x, 2) + 
                Math.pow(points[i].y - points[i-1].y, 2)
            );
            distances.push(dist);
            speeds.push(points[i].speed);
        }

        // 统计距离
        const avgDistance = distances.reduce((a, b) => a + b, 0) / distances.length;
        const distanceVariance = distances.reduce((sum, dist) => sum + Math.pow(dist - avgDistance, 2), 0) / distances.length;
        const distanceStdDev = Math.sqrt(distanceVariance);
        const maxDistance = Math.max(...distances);
        const minDistance = Math.min(...distances);

        // 计算速度变化
        let speedVariation = 0;
        if (speeds.length > 1) {
            for (let i = 1; i < speeds.length; i++) {
                speedVariation += Math.abs(speeds[i] - speeds[i-1]);
            }
            speedVariation /= speeds.length - 1;
        }

        return {
            totalPoints: points.length,
            avgDistance,
            distanceStdDev,
            maxDistance,
            minDistance,
            speedVariation
        };
    }

    /**
     * 获取策略名称
     */
    private getStrategyName(strategy: eSamplingStrategy): string {
        switch (strategy) {
            case eSamplingStrategy.UniformDistance:
                return "均匀距离采样";
            case eSamplingStrategy.AdaptiveSubdivision:
                return "自适应细分";
            default:
                return "未知策略";
        }
    }

    /**
     * 显示结果
     */
    private logResult(message: string) {
        console.log("[PathSamplingDebug]", message);
        if (this.resultLabel) {
            this.resultLabel.string = message;
        }
    }

    /**
     * 设置测试参数
     */
    public setTestParameters(targetDistance: number, smoothingFactor: number) {
        this.targetSamplingDistance = targetDistance;
        this.speedSmoothingFactor = smoothingFactor;
    }

    /**
     * 获取推荐设置
     */
    public getRecommendedSettings(): { strategy: eSamplingStrategy, distance: number } {
        // 基于路径复杂度推荐设置
        if (!this.testPathData) {
            return { strategy: eSamplingStrategy.UniformDistance, distance: 20 };
        }

        const points = this.testPathData.points;
        if (points.length < 3) {
            return { strategy: eSamplingStrategy.UniformDistance, distance: 10 };
        }

        // 计算路径的平均速度
        const avgSpeed = points.reduce((sum, p) => sum + p.speed, 0) / points.length;
        
        // 基于速度推荐采样距离
        const recommendedDistance = Math.max(5, Math.min(50, avgSpeed / 30));

        return {
            strategy: eSamplingStrategy.UniformDistance,
            distance: recommendedDistance
        };
    }
}
