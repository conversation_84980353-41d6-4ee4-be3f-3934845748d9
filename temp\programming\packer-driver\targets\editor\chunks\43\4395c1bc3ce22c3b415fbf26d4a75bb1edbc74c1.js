System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, CCInteger, CCFloat, Enum, Button, Label, PathData, eSamplingStrategy, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _crd, ccclass, property, PathSamplingDebugHelper;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "./PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSamplingStrategy(extras) {
    _reporterNs.report("eSamplingStrategy", "./PathData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      CCInteger = _cc.CCInteger;
      CCFloat = _cc.CCFloat;
      Enum = _cc.Enum;
      Button = _cc.Button;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      PathData = _unresolved_2.PathData;
      eSamplingStrategy = _unresolved_2.eSamplingStrategy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fc04dRDi2JAdYl7S+8V3xso", "PathSamplingDebugHelper", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'CCInteger', 'CCFloat', 'Enum', 'Button', 'Label']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 路径采样调试助手
       * 用于测试和比较不同的采样策略效果
       */

      _export("PathSamplingDebugHelper", PathSamplingDebugHelper = (_dec = ccclass('PathSamplingDebugHelper'), _dec2 = property({
        type: _crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
          error: Error()
        }), PathData) : PathData,
        displayName: "测试路径数据"
      }), _dec3 = property({
        type: Enum(_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
          error: Error()
        }), eSamplingStrategy) : eSamplingStrategy),
        displayName: "当前采样策略"
      }), _dec4 = property({
        type: CCInteger,
        displayName: "目标采样距离",
        tooltip: "仅对均匀距离采样有效"
      }), _dec5 = property({
        type: CCFloat,
        displayName: "速度平滑系数",
        range: [0, 1],
        slide: true
      }), _dec6 = property({
        type: Button,
        displayName: "测试当前策略"
      }), _dec7 = property({
        type: Button,
        displayName: "比较所有策略"
      }), _dec8 = property({
        type: Label,
        displayName: "结果显示"
      }), _dec(_class = (_class2 = class PathSamplingDebugHelper extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "testPathData", _descriptor, this);

          _initializerDefineProperty(this, "currentStrategy", _descriptor2, this);

          _initializerDefineProperty(this, "targetSamplingDistance", _descriptor3, this);

          _initializerDefineProperty(this, "speedSmoothingFactor", _descriptor4, this);

          _initializerDefineProperty(this, "testButton", _descriptor5, this);

          _initializerDefineProperty(this, "compareButton", _descriptor6, this);

          _initializerDefineProperty(this, "resultLabel", _descriptor7, this);
        }

        onLoad() {
          if (this.testButton) {
            this.testButton.node.on(Button.EventType.CLICK, this.testCurrentStrategy, this);
          }

          if (this.compareButton) {
            this.compareButton.node.on(Button.EventType.CLICK, this.compareAllStrategies, this);
          }
        }
        /**
         * 测试当前采样策略
         */


        testCurrentStrategy() {
          if (!this.testPathData) {
            this.logResult("错误：未设置测试路径数据");
            return;
          } // 备份原始设置


          const originalStrategy = this.testPathData.samplingStrategy; // 应用测试设置

          this.testPathData.samplingStrategy = this.currentStrategy; // 生成采样点

          const startTime = performance.now();
          const sampledPoints = this.testPathData.getSubdividedPoints(true);
          const endTime = performance.now(); // 分析结果

          const analysis = this.analyzePathSampling(sampledPoints);
          const processingTime = endTime - startTime; // 恢复原始设置

          this.testPathData.samplingStrategy = originalStrategy; // 显示结果

          const strategyName = this.getStrategyName(this.currentStrategy);
          const result = `
策略: ${strategyName}
处理时间: ${processingTime.toFixed(2)}ms
采样点数: ${analysis.totalPoints}
平均距离: ${analysis.avgDistance.toFixed(2)}px
距离标准差: ${analysis.distanceStdDev.toFixed(2)}px
最大距离: ${analysis.maxDistance.toFixed(2)}px
最小距离: ${analysis.minDistance.toFixed(2)}px
速度变化: ${analysis.speedVariation.toFixed(2)}
        `;
          this.logResult(result);
        }
        /**
         * 比较所有采样策略
         */


        compareAllStrategies() {
          if (!this.testPathData) {
            this.logResult("错误：未设置测试路径数据");
            return;
          }

          const strategies = [(_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
            error: Error()
          }), eSamplingStrategy) : eSamplingStrategy).UniformDistance, (_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
            error: Error()
          }), eSamplingStrategy) : eSamplingStrategy).AdaptiveSubdivision];
          const results = []; // 备份原始设置

          const originalStrategy = this.testPathData.samplingStrategy;

          for (const strategy of strategies) {
            this.testPathData.samplingStrategy = strategy;
            const startTime = performance.now();
            const sampledPoints = this.testPathData.getSubdividedPoints(true);
            const endTime = performance.now();
            const analysis = this.analyzePathSampling(sampledPoints);
            const processingTime = endTime - startTime;
            const strategyName = this.getStrategyName(strategy);
            results.push(`
${strategyName}:
  时间: ${processingTime.toFixed(2)}ms
  点数: ${analysis.totalPoints}
  平均距离: ${analysis.avgDistance.toFixed(2)}px
  距离标准差: ${analysis.distanceStdDev.toFixed(2)}px
  速度变化: ${analysis.speedVariation.toFixed(2)}
            `);
          } // 恢复原始设置


          this.testPathData.samplingStrategy = originalStrategy; // 显示比较结果

          const compareResult = "=== 采样策略比较 ===" + results.join("\n");
          this.logResult(compareResult);
        }
        /**
         * 分析路径采样质量
         */


        analyzePathSampling(points) {
          if (points.length < 2) {
            return {
              totalPoints: points.length,
              avgDistance: 0,
              distanceStdDev: 0,
              maxDistance: 0,
              minDistance: 0,
              speedVariation: 0
            };
          } // 计算相邻点距离


          const distances = [];
          const speeds = [];

          for (let i = 1; i < points.length; i++) {
            const dist = Math.sqrt(Math.pow(points[i].x - points[i - 1].x, 2) + Math.pow(points[i].y - points[i - 1].y, 2));
            distances.push(dist);
            speeds.push(points[i].speed);
          } // 统计距离


          const avgDistance = distances.reduce((a, b) => a + b, 0) / distances.length;
          const distanceVariance = distances.reduce((sum, dist) => sum + Math.pow(dist - avgDistance, 2), 0) / distances.length;
          const distanceStdDev = Math.sqrt(distanceVariance);
          const maxDistance = Math.max(...distances);
          const minDistance = Math.min(...distances); // 计算速度变化

          let speedVariation = 0;

          if (speeds.length > 1) {
            for (let i = 1; i < speeds.length; i++) {
              speedVariation += Math.abs(speeds[i] - speeds[i - 1]);
            }

            speedVariation /= speeds.length - 1;
          }

          return {
            totalPoints: points.length,
            avgDistance,
            distanceStdDev,
            maxDistance,
            minDistance,
            speedVariation
          };
        }
        /**
         * 获取策略名称
         */


        getStrategyName(strategy) {
          switch (strategy) {
            case (_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
              error: Error()
            }), eSamplingStrategy) : eSamplingStrategy).UniformDistance:
              return "均匀距离采样";

            case (_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
              error: Error()
            }), eSamplingStrategy) : eSamplingStrategy).AdaptiveSubdivision:
              return "自适应细分";

            default:
              return "未知策略";
          }
        }
        /**
         * 显示结果
         */


        logResult(message) {
          console.log("[PathSamplingDebug]", message);

          if (this.resultLabel) {
            this.resultLabel.string = message;
          }
        }
        /**
         * 设置测试参数
         */


        setTestParameters(targetDistance, smoothingFactor) {
          this.targetSamplingDistance = targetDistance;
          this.speedSmoothingFactor = smoothingFactor;
        }
        /**
         * 获取推荐设置
         */


        getRecommendedSettings() {
          // 基于路径复杂度推荐设置
          if (!this.testPathData) {
            return {
              strategy: (_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
                error: Error()
              }), eSamplingStrategy) : eSamplingStrategy).UniformDistance,
              distance: 20
            };
          }

          const points = this.testPathData.points;

          if (points.length < 3) {
            return {
              strategy: (_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
                error: Error()
              }), eSamplingStrategy) : eSamplingStrategy).UniformDistance,
              distance: 10
            };
          } // 计算路径的平均速度


          const avgSpeed = points.reduce((sum, p) => sum + p.speed, 0) / points.length; // 基于速度推荐采样距离

          const recommendedDistance = Math.max(5, Math.min(50, avgSpeed / 30));
          return {
            strategy: (_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
              error: Error()
            }), eSamplingStrategy) : eSamplingStrategy).UniformDistance,
            distance: recommendedDistance
          };
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "testPathData", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "currentStrategy", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eSamplingStrategy === void 0 ? (_reportPossibleCrUseOfeSamplingStrategy({
            error: Error()
          }), eSamplingStrategy) : eSamplingStrategy).UniformDistance;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "targetSamplingDistance", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 20;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "speedSmoothingFactor", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.3;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "testButton", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "compareButton", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "resultLabel", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4395c1bc3ce22c3b415fbf26d4a75bb1edbc74c1.js.map