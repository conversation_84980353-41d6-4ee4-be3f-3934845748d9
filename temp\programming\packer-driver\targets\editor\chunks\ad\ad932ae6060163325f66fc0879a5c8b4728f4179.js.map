{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMoveDebugHelper.ts"], "names": ["_decorator", "Component", "PathMove", "ccclass", "property", "PathMoveDebugHelper", "displayName", "tooltip", "onLoad", "setDebugEnabled", "enableDebug", "updateThresholds", "setDebugThresholds", "positionJump", "positionJumpThreshold", "singleFrameMove", "singleFrameMoveThreshold", "acceleration", "accelerationThreshold", "frameTime", "frameTimeThreshold", "toggleDebug", "enableDebugLog", "disableDebugLog", "setHighSensitivity", "setLowSensitivity"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;AAE9B;AACA;AACA;AACA;;qCAEaK,mB,WADZF,OAAO,CAAC,qBAAD,C,UAGHC,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRF,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,YAAf;AAA6BC,QAAAA,OAAO,EAAE;AAAtC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,YAAf;AAA6BC,QAAAA,OAAO,EAAE;AAAtC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,OAAf;AAAwBC,QAAAA,OAAO,EAAE;AAAjC,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE,WAAf;AAA4BC,QAAAA,OAAO,EAAE;AAArC,OAAD,C,2BAfb,MACaF,mBADb,SACyCJ,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAiB/CO,QAAAA,MAAM,GAAG;AACL;AACA;AAAA;AAAA,oCAASC,eAAT,CAAyB,KAAKC,WAA9B,EAFK,CAIL;;AACA,eAAKC,gBAAL;AACH;AAED;AACJ;AACA;;;AACWA,QAAAA,gBAAgB,GAAG;AACtB;AAAA;AAAA,oCAASC,kBAAT,CAA4B;AACxBC,YAAAA,YAAY,EAAE,KAAKC,qBADK;AAExBC,YAAAA,eAAe,EAAE,KAAKC,wBAFE;AAGxBC,YAAAA,YAAY,EAAE,KAAKC,qBAHK;AAIxBC,YAAAA,SAAS,EAAE,KAAKC,kBAAL,GAA0B,IAJb,CAIkB;;AAJlB,WAA5B;AAMH;AAED;AACJ;AACA;;;AACWC,QAAAA,WAAW,GAAG;AACjB,eAAKX,WAAL,GAAmB,CAAC,KAAKA,WAAzB;AACA;AAAA;AAAA,oCAASD,eAAT,CAAyB,KAAKC,WAA9B;AACH;AAED;AACJ;AACA;;;AACWY,QAAAA,cAAc,GAAG;AACpB,eAAKZ,WAAL,GAAmB,IAAnB;AACA;AAAA;AAAA,oCAASD,eAAT,CAAyB,IAAzB;AACH;AAED;AACJ;AACA;;;AACWc,QAAAA,eAAe,GAAG;AACrB,eAAKb,WAAL,GAAmB,KAAnB;AACA;AAAA;AAAA,oCAASD,eAAT,CAAyB,KAAzB;AACH;AAED;AACJ;AACA;;;AACWe,QAAAA,kBAAkB,GAAG;AACxB,eAAKV,qBAAL,GAA6B,CAA7B;AACA,eAAKE,wBAAL,GAAgC,EAAhC;AACA,eAAKE,qBAAL,GAA6B,IAA7B;AACA,eAAKE,kBAAL,GAA0B,EAA1B;AACA,eAAKT,gBAAL;AACH;AAED;AACJ;AACA;;;AACWc,QAAAA,iBAAiB,GAAG;AACvB,eAAKX,qBAAL,GAA6B,EAA7B;AACA,eAAKE,wBAAL,GAAgC,GAAhC;AACA,eAAKE,qBAAL,GAA6B,IAA7B;AACA,eAAKE,kBAAL,GAA0B,EAA1B;AACA,eAAKT,gBAAL;AACH;;AAjF8C,O;;;;;iBAGjB,I;;;;;;;iBAGS,E;;;;;;;iBAGG,E;;;;;;;iBAGH,I;;;;;;;iBAGH,E", "sourcesContent": ["import { _decorator, Component } from 'cc';\nimport { PathMove } from './PathMove';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * PathMove 调试助手\n * 用于快速启用/禁用 PathMove 的调试日志，并配置调试阈值\n */\n@ccclass('PathMoveDebugHelper')\nexport class PathMoveDebugHelper extends Component {\n\n    @property({ displayName: \"启用调试日志\" })\n    public enableDebug: boolean = true;\n\n    @property({ displayName: \"位置跳跃阈值(像素)\", tooltip: \"超过此值会警告位置跳跃\" })\n    public positionJumpThreshold: number = 10;\n\n    @property({ displayName: \"单帧移动阈值(像素)\", tooltip: \"超过此值会警告单帧移动过大\" })\n    public singleFrameMoveThreshold: number = 50;\n\n    @property({ displayName: \"加速度阈值\", tooltip: \"超过此值会警告加速度过大\" })\n    public accelerationThreshold: number = 2000;\n\n    @property({ displayName: \"帧时间阈值(毫秒)\", tooltip: \"超过此值会警告帧时间过长\" })\n    public frameTimeThreshold: number = 20;\n\n    onLoad() {\n        // 设置调试状态\n        PathMove.setDebugEnabled(this.enableDebug);\n\n        // 设置调试阈值\n        this.updateThresholds();\n    }\n\n    /**\n     * 更新调试阈值\n     */\n    public updateThresholds() {\n        PathMove.setDebugThresholds({\n            positionJump: this.positionJumpThreshold,\n            singleFrameMove: this.singleFrameMoveThreshold,\n            acceleration: this.accelerationThreshold,\n            frameTime: this.frameTimeThreshold / 1000 // 转换为秒\n        });\n    }\n\n    /**\n     * 在运行时切换调试状态\n     */\n    public toggleDebug() {\n        this.enableDebug = !this.enableDebug;\n        PathMove.setDebugEnabled(this.enableDebug);\n    }\n\n    /**\n     * 启用调试日志\n     */\n    public enableDebugLog() {\n        this.enableDebug = true;\n        PathMove.setDebugEnabled(true);\n    }\n\n    /**\n     * 禁用调试日志\n     */\n    public disableDebugLog() {\n        this.enableDebug = false;\n        PathMove.setDebugEnabled(false);\n    }\n\n    /**\n     * 设置为高敏感度（更容易触发警告）\n     */\n    public setHighSensitivity() {\n        this.positionJumpThreshold = 5;\n        this.singleFrameMoveThreshold = 30;\n        this.accelerationThreshold = 1000;\n        this.frameTimeThreshold = 16;\n        this.updateThresholds();\n    }\n\n    /**\n     * 设置为低敏感度（不容易触发警告）\n     */\n    public setLowSensitivity() {\n        this.positionJumpThreshold = 20;\n        this.singleFrameMoveThreshold = 100;\n        this.accelerationThreshold = 5000;\n        this.frameTimeThreshold = 33;\n        this.updateThresholds();\n    }\n}\n"]}