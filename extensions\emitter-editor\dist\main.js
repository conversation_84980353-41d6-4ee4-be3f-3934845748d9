"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
function genEnum(jsonFileName, enumName) {
    try {
        // @ts-ignore
        const projectPath = Editor.Project.path;
        const jsonPath = path.join(projectPath, 'assets', 'bundles', 'luban', jsonFileName + '.json');
        const outputPath = path.join(projectPath, 'assets', 'editor', 'enum-gen', enumName + '.ts');
        // 检查输入文件是否存在
        if (!fs.existsSync(jsonPath)) {
            console.warn('json file not found:', jsonPath);
            return;
        }
        // 读取JSON文件
        const jsonContent = fs.readFileSync(jsonPath, 'utf8');
        const bulletData = JSON.parse(jsonContent);
        if (!Array.isArray(bulletData)) {
            console.warn('json is not an array');
            return;
        }
        // 生成枚举内容
        let enumContent = `export enum ${enumName} {\n`;
        bulletData.forEach((item) => {
            if (item.id) {
                // remove '-'
                item.name = item.name.replaceAll('-', '');
                // fix name start with digit(s)
                item.name = item.name.replace(/^(\d)/, 'D$1');
                if (item.name && item.name.trim() !== '') {
                    enumContent += `    ${item.name} = ${item.id},\n`;
                }
                else {
                    enumContent += `    ${item.id} = ${item.id},\n`;
                }
            }
        });
        enumContent += '}\n';
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        // 写入文件
        fs.writeFileSync(outputPath, enumContent, 'utf8');
        console.log(`${enumName}.ts generated successfully at:`, outputPath);
        // 刷新资源数据库
        // @ts-ignore
        Editor.Message.request('asset-db', 'refresh-asset', `db://assets/editor/enum-gen/${enumName}.ts`);
    }
    catch (error) {
        console.error(`Error creating ${enumName}:`, error);
    }
}
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
exports.methods = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    movePlayerUp() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerUp',
            args: []
        });
    },
    movePlayerDown() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerDown',
            args: []
        });
    },
    movePlayerLeft() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerLeft',
            args: []
        });
    },
    movePlayerRight() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerRight',
            args: []
        });
    },
    /**
     * 监听asset-db:asset-changed消息
     */
    onAssetChanged(_uuid, info) {
        console.log('asset changed:', _uuid, info);
        if (info && info.path) {
            if (info.path.includes('tbresemitter')) {
                console.log('tbresemitter.json file changed, regenerating EmitterEnum...');
                exports.methods.createEmitterEnum();
            }
            if (info.path.includes('tbresenemy')) {
                console.log('tbresenemy.json file changed, regenerating EnemyEnum...');
                exports.methods.createEnemyEnum();
            }
        }
    },
    /**
     * 创建EmitterEnum枚举文件
     */
    createEmitterEnum() {
        genEnum('tbresemitter', 'EmitterEnum');
    },
    createEnemyEnum() {
        genEnum('tbresenemy', 'EnemyEnum');
    }
};
/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
function load() {
    console.log('emitter-editor extension loaded');
    console.log('Available methods:', Object.keys(exports.methods));
    // 初始化时生成一次EmitterEnum
    exports.methods.createEmitterEnum();
    exports.methods.createEnemyEnum();
}
/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
function unload() { }
//# sourceMappingURL=data:application/json;base64,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