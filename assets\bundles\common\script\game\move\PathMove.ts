import { _decorator, Component, misc, Enum, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { MoveBase, eMoveEvent, eOrientationType } from './IMovable';
import Entity from '../ui/base/Entity';
import { PathData, PathPoint } from '../data/PathData';
import { DefaultMove } from './DefaultMove';

const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PathMove')
@executeInEditMode()
export class PathMove extends DefaultMove {
    public _pathAsset: JsonAsset | null = null;
    @property({ type: JsonAsset, displayName: "路径数据(预览用)" })
    public get pathAsset(): JsonAsset | null {
        return this._pathAsset;
    }
    public set pathAsset(value: JsonAsset) {
        this._pathAsset = value;
        if (value) {
            this.setPath(PathData.fromJSON(value.json));
        }
    }

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "反向移动" })
    public reverse: boolean = false;

    @property({ type: Enum(eOrientationType), displayName:"朝向类型" })
    public get editor_orientationType(): eOrientationType {
        return this.orientationType;
    }
    public set editor_orientationType(value: eOrientationType) {
        this.setOrientation(value, this.editor_orientationParam);
    }
    @property({ displayName: "朝向参数" })
    public get editor_orientationParam(): number {
        return this.orientationParam;
    }
    public set editor_orientationParam(value: number) {
        this.setOrientation(this.orientationType, value);
    }

    // 路径相关数据
    private _pathData: PathData | null = null;
    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）
    // 路径偏移
    private _offsetX: number = 0;
    private _offsetY: number = 0;
    // 平滑过渡参数（1-25)
    private static readonly kLerpFactor = 16;

    // [DEBUG] 调试开关
    private static DEBUG_ENABLED = true;
    private static debugLog(message: string) {
        if (PathMove.DEBUG_ENABLED) {
            console.log(message);
        }
    }
    private static debugWarn(message: string) {
        if (PathMove.DEBUG_ENABLED) {
            console.warn(message);
        }
    }

    // 公共方法：启用/禁用调试日志
    public static setDebugEnabled(enabled: boolean) {
        PathMove.DEBUG_ENABLED = enabled;
        console.log(`[PathMove] 调试日志 ${enabled ? '启用' : '禁用'}`);
    }

    // 移动状态
    private _currentPointIndex: number = 0; // 当前所在的细分点索引
    private _nextPointIndex: number = 0;
    private _remainDistance: number = 0;    // 距离下一个点的剩余距离

    // 停留状态
    private _stayTimer: number = 0; // 停留计时器（秒）

    private _updateInEditor: boolean = false;
    public onFocusInEditor() {
        this._updateInEditor = true;
        this._isMovable = true;
    }
    public onLostFocusInEditor() {
        this._updateInEditor = false;
        this._isMovable = false;
        this.resetToStart();
    }
    public update(dt: number) {
        if (this._updateInEditor) {
            this.tick(dt);
        }
    }

    // 注意调用顺序,先调用setOffset,再调用setPath
    public setOffset(x: number, y: number): PathMove {
        this._offsetX = x;
        this._offsetY = y;
        return this;
    }
    public setPath(pathData: PathData): PathMove {
        this._pathData = pathData;
        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组
        this._subdivided = this._pathData.getSubdividedPoints();
        this.resetToStart();

        return this;
    }

    /**
     * 主要的移动更新逻辑
     */
    public tick(dt: number): void {
        if (!this._isMovable) return;
        if (!this._pathData) {
            super.tick(dt);
            return;
        }

        // [DEBUG] 记录基本状态
        if (dt > 0.02) { // 如果帧时间超过20ms，记录警告
            PathMove.debugWarn(`[PathMove] 帧时间过长: ${(dt * 1000).toFixed(2)}ms, 可能导致移动不平滑`);
        }

        // 处理停留逻辑
        if (this._stayTimer > 0) {
            this._stayTimer -= dt;
            if (this._stayTimer <= 0) {
                this._stayTimer = 0;
                PathMove.debugLog(`[PathMove] 停留结束，继续移动到下一个点`);
                // 停留结束，继续移动到下一个点
                this.moveToNextPoint();
            }
        } else if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
        }

        // [DEBUG] 记录tilting状态
        const positionBeforeTilt = { x: this._position.x, y: this._position.y };
        this.updateTilting(this.speedAngle, dt, this._position);
        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            const tiltDeltaX = this._position.x - positionBeforeTilt.x;
            const tiltDeltaY = this._position.y - positionBeforeTilt.y;
            if (Math.abs(tiltDeltaX) > 1 || Math.abs(tiltDeltaY) > 1) {
                PathMove.debugLog(`[PathMove] Tilting偏移: (${tiltDeltaX.toFixed(2)}, ${tiltDeltaY.toFixed(2)}), tiltSpeed: ${this.tiltSpeed}, tiltOffset: ${this.tiltOffset}`);
            }
        }

        if (Math.abs(this.speed) > 0.001 || Math.abs(this.tiltSpeed) > 0.001) {
            // 设置节点位置
            this.node.setPosition(this._position);
            this.checkVisibility();

            // 更新朝向
            this.updateOrientation(dt);
        }
    }

    private tickMovement(dt: number) {
        // 使用匀加速直线运动更新位置
        const v0 = this.speed;
        // s = v0*t + 0.5*a*t^2
        let s = v0 * dt + 0.5 * this.acceleration * dt * dt;

        // [DEBUG] 记录运动计算
        const originalS = s;
        const originalDt = dt;

        if (s > this._remainDistance) {
            s = this._remainDistance;
            // 重新计算实际需要的时间
            if (v0 + this.acceleration * dt !== 0) {
                dt = s / (v0 + 0.5 * this.acceleration * dt);
            }
            PathMove.debugLog(`[PathMove] 距离限制: 原始移动距离 ${originalS.toFixed(2)} -> 实际移动距离 ${s.toFixed(2)}, dt: ${originalDt.toFixed(4)} -> ${dt.toFixed(4)}`);
        }

        const newSpeed = this.speed + this.acceleration * dt;

        // [DEBUG] 记录速度和加速度变化
        if (Math.abs(this.acceleration) > 0.1) {
            PathMove.debugLog(`[PathMove] 运动状态: v0=${v0.toFixed(2)}, a=${this.acceleration.toFixed(2)}, s=${s.toFixed(2)}, newSpeed=${newSpeed.toFixed(2)}, remainDist=${this._remainDistance.toFixed(2)}`);
        }

        this.speed = newSpeed;

        // 计算移动向量
        const angleRad = this.speedAngle;
        const deltaX = Math.cos(angleRad) * s;
        const deltaY = Math.sin(angleRad) * s;

        // [DEBUG] 记录位置变化 (暂时注释掉未使用的变量)
        // const oldPos = { x: this._position.x, y: this._position.y };

        // 更新位置
        this._position.x += deltaX;
        this._position.y += deltaY;

        // [DEBUG] 检查位置变化是否异常
        const positionDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        if (positionDelta > 50) { // 如果单帧移动距离超过50像素
            PathMove.debugWarn(`[PathMove] 单帧移动距离过大: ${positionDelta.toFixed(2)}, delta=(${deltaX.toFixed(2)}, ${deltaY.toFixed(2)}), 角度=${(angleRad * 180 / Math.PI).toFixed(2)}°`);
        }

        // 检查是否到达目标点
        if (this._remainDistance > 0) {
            this._remainDistance -= s;
            if (this._remainDistance <= 0) {
                PathMove.debugLog(`[PathMove] 到达目标点 ${this._nextPointIndex}, 剩余距离: ${this._remainDistance.toFixed(2)}`);
                this.onReachPoint(this._nextPointIndex);
            }
        }
    }

    protected getDesiredOrientation(dt: number): number {
        if (this._pathData && this.orientationType === eOrientationType.Path) {
            // PathMove这里speedAngle是radians，需要转换为degrees进行角度lerp
            const targetAngleDegrees = radiansToDegrees(this.speedAngle);
            const oldOrientation = this.orientation;
            const newOrientation = PathMove.lerpAngle(this.orientation, targetAngleDegrees, PathMove.kLerpFactor, dt);

            // [DEBUG] 记录朝向变化
            const orientationDiff = Math.abs(newOrientation - oldOrientation);
            if (orientationDiff > 10) { // 如果朝向变化超过10度
                PathMove.debugLog(`[PathMove] 朝向变化: ${oldOrientation.toFixed(2)}° -> ${newOrientation.toFixed(2)}° (目标: ${targetAngleDegrees.toFixed(2)}°), 变化量: ${orientationDiff.toFixed(2)}°`);
            }

            return newOrientation;
        }
        return super.getDesiredOrientation(dt);
    }

    private onReachPoint(pointIndex: number) {
        // 更新当前点索引
        this._currentPointIndex = pointIndex;

        // [DEBUG] 记录到达点信息
        PathMove.debugLog(`[PathMove] 到达路径点 ${pointIndex}`);

        // 检查是否需要停留
        const currentPoint = this.getPathPoint(pointIndex);
        if (currentPoint) {
            // [DEBUG] 记录位置更新
            const oldBasePos = { x: this._basePosition.x, y: this._basePosition.y };
            this._basePosition.x = currentPoint.x + this._offsetX;
            this._basePosition.y = currentPoint.y + this._offsetY;

            const positionJump = Math.sqrt(
                Math.pow(this._basePosition.x - oldBasePos.x, 2) +
                Math.pow(this._basePosition.y - oldBasePos.y, 2)
            );
            if (positionJump > 5) { // 如果位置跳跃超过5像素
                PathMove.debugWarn(`[PathMove] 位置跳跃: ${positionJump.toFixed(2)}, 从 (${oldBasePos.x.toFixed(2)}, ${oldBasePos.y.toFixed(2)}) 到 (${this._basePosition.x.toFixed(2)}, ${this._basePosition.y.toFixed(2)})`);
            }

            // 设置速度
            const oldSpeed = this.speed;
            this.speed = currentPoint.speed;
            if (Math.abs(this.speed - oldSpeed) > 50) { // 如果速度变化超过50
                PathMove.debugLog(`[PathMove] 速度变化: ${oldSpeed.toFixed(2)} -> ${this.speed.toFixed(2)}`);
            }

            // 设置朝向
            this.orientationType = currentPoint.orientationType;
            this.orientationParam = currentPoint.orientationParam;

            // 这里要考虑的问题是: 第一个点的初始朝向,希望是立刻生效,而不是lerp
            if (pointIndex === 0) {
                this._position.set(this._basePosition);
                this.node.setPosition(this._position);
                PathMove.debugLog(`[PathMove] 初始化位置: (${this._position.x.toFixed(2)}, ${this._position.y.toFixed(2)})`);

                const nextPoint = this.getPathPoint(pointIndex + 1);
                if (nextPoint) {
                    const dirX = nextPoint.x - currentPoint.x;
                    const dirY = nextPoint.y - currentPoint.y;
                    this.speedAngle = Math.atan2(dirY, dirX);
                    PathMove.debugLog(`[PathMove] 初始化角度: ${(this.speedAngle * 180 / Math.PI).toFixed(2)}°`);
                    if (this.orientationType === eOrientationType.Path) {
                        this.orientation = radiansToDegrees(this.speedAngle);
                    }
                }
                this.updateOrientation(0);
            }

            PathMove.debugLog(`[PathMove] 到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);
            if (currentPoint.stayDuration > 0) {
                this._stayTimer = currentPoint.stayDuration / 1000.0;
                return;
            }
        } else {
            PathMove.debugWarn(`[PathMove] 无法获取路径点 ${pointIndex}, 停止移动`);
            this.speed = 0;
        }

        // 继续移动到下一个点
        this.moveToNextPoint();
    }

    private moveToNextPoint() {
        const nextIndex = this._currentPointIndex + 1;
        if (nextIndex >= this._subdivided.length) {
            // 到达路径终点
            if (this.loop) {
                // 循环模式，回到起点
                this.setNext(0);
                this.emit(eMoveEvent.onPathLoop);
            } else {
                // 停止移动
                this._nextPointIndex = this._currentPointIndex;
                this.emit(eMoveEvent.onPathEnd);
            }
        } else {
            // 移动到下一个点
            this.setNext(nextIndex);
        }
    }

    private setNext(pathPointIndex: number) {
        this._nextPointIndex = pathPointIndex;

        const currentPoint = this.getPathPoint(this._currentPointIndex);
        const nextPoint = this.getPathPoint(this._nextPointIndex);
        if (currentPoint && nextPoint) {
            const dirX = nextPoint.x - currentPoint.x;
            const dirY = nextPoint.y - currentPoint.y;
            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);

            if (this._remainDistance > 1) {
                // 计算目标移动角度
                const newAngle = Math.atan2(dirY, dirX);
                const oldAngle = this.speedAngle;

                // [DEBUG] 检查角度变化是否过大
                let angleDiff = newAngle - oldAngle;
                // 标准化角度差到 [-π, π] 范围
                while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
                while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;

                if (Math.abs(angleDiff) > Math.PI / 4) { // 如果角度变化超过45度
                    PathMove.debugWarn(`[PathMove] 角度变化过大: ${(angleDiff * 180 / Math.PI).toFixed(2)}°, 从 ${(oldAngle * 180 / Math.PI).toFixed(2)}° 到 ${(newAngle * 180 / Math.PI).toFixed(2)}°`);
                }

                this.speedAngle = newAngle;

                // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x
                // 解出 a = (v1^2 - v0^2) / (2*x)
                const v0 = currentPoint.speed;
                const v1 = nextPoint.speed;
                this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance);

                // [DEBUG] 记录路径段设置
                PathMove.debugLog(`[PathMove] 设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${(newAngle * 180 / Math.PI).toFixed(2)}°`);

                // [DEBUG] 检查加速度是否异常
                if (Math.abs(this.acceleration) > 2000) {
                    PathMove.debugWarn(`[PathMove] 加速度过大: ${this.acceleration.toFixed(2)}, 可能导致移动不平滑`);
                }
            } else {
                PathMove.debugLog(`[PathMove] 距离过小 (${this._remainDistance.toFixed(2)}), 跳过此段`);
            }
        }
    }

    private getPathPoint(pathPointIndex: number): PathPoint | null {
        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
        }
        return this._subdivided[pathPointIndex];
    }

    private resetToStart() {
        this._currentPointIndex = 0;
        this._nextPointIndex = 0;
        this._stayTimer = 0;
        this._tiltTime = 0;
        this._remainDistance = 0;
        this.speed = 0;
        this.acceleration = 0;
        this.tiltSpeed = 0;
        this.tiltOffset = 0;

        this.onReachPoint(0);
    }

    public isStaying(): boolean {
        return this._stayTimer > 0;
    }

    public getRemainingStayTime(): number {
        return this._stayTimer;
    }

    // static lerp(a: number, b: number, decay: number, dt: number): number {
    //     return (a - b) * Math.exp(-decay * dt) + b;
    // }

    /**
     * 角度插值，正确处理角度环绕问题
     * @param from 起始角度（度）
     * @param to 目标角度（度）
     * @param decay 衰减系数
     * @param dt 时间增量
     * @returns 插值后的角度（度）
     */
    static lerpAngle(from: number, to: number, decay: number, dt: number): number {
        // 将角度标准化到[-180, 180]范围
        const normalizeAngle = (angle: number): number => {
            while (angle > 180) angle -= 360;
            while (angle < -180) angle += 360;
            return angle;
        };

        // 标准化输入角度
        from = normalizeAngle(from);
        to = normalizeAngle(to);

        // 计算角度差，选择最短路径
        let diff = to - from;
        if (diff > 180) diff -= 360;
        if (diff < -180) diff += 360;

        // 使用指数衰减插值
        const lerpedDiff = diff * (1 - Math.exp(-decay * dt));
        return normalizeAngle(from + lerpedDiff);
    }
}
