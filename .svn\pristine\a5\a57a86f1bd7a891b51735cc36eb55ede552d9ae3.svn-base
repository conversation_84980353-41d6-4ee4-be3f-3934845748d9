import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Graphics, Color, Vec3, ValueType, CCBoolean, CCString, ImageAsset, resources, assetManager, UITransform, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, AudioClip, BitMask } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LevelDataEvent } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { LevelDataEventTrigger, LevelDataEventTriggerType } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger';
import { LevelDataEventTriggerLog } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog';
import { LevelDataEventTriggerAudio } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio';
import { LevelDataEventTriggerWave, LevelDataEventWave, LevelDataEventWaveGroup } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave';
import { LevelDataEventTriggerSpecialEvent, eLevelSpecialEvent } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent';
import { newTrigger } from 'db://assets/bundles/common/script/leveldata/trigger/newTrigger';
import { LevelDataEventCondtionType } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionWave } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave';
import { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';
import { WavePreview } from './preview/WavePreview';
import { newCondition } from 'db://assets/bundles/common/script/leveldata/condition/newCondition';

import { LevelEditorElemUI, LevelLayerElemLayer } from './LevelEditorElemUI';
import { LevelEditorWaveGroup } from './LevelEditorWaveParam';
import { LevelEditorCondition } from './LevelEditorCondition';
import { GameConst } from '../../scripts/core/base/GameConst';

@ccclass('LevelEditorEventTrigger')
export class LevelEditorEventTrigger {
    public _index = 0;
    @property({
        type:Enum(LevelDataEventTriggerType),
    })
    public dataType: LevelDataEventTriggerType = LevelDataEventTriggerType.Log;

    private dataMap: Map<LevelDataEventTriggerType, LevelDataEventTrigger> = new Map();
    constructor() {
        for (let t = 0; t < LevelDataEventTriggerType.Max; t++) {
            this.dataMap.set(t, newTrigger({_type: t}));
        }
    }

    // 根据type返回当前类型
    public get data(): LevelDataEventTrigger {
        return this.getData(this.dataType);
    }
    public set data(value: LevelDataEventTrigger) {
        this.dataType = value._type;
        this.dataMap.set(this.dataType, value);
    }

    private getData<T extends LevelDataEventTrigger>(type: LevelDataEventTriggerType): T {
        return this.dataMap.get(type)! as T;
    }
    private get logData(): LevelDataEventTriggerLog {
        return this.getData<LevelDataEventTriggerLog>(LevelDataEventTriggerType.Log);
    }
    private get audioData(): LevelDataEventTriggerAudio {
        return this.getData<LevelDataEventTriggerAudio>(LevelDataEventTriggerType.Audio);
    }
    private get waveData(): LevelDataEventTriggerWave {
        return this.getData<LevelDataEventTriggerWave>(LevelDataEventTriggerType.Wave);
    }
    private get specialEventData(): LevelDataEventTriggerSpecialEvent {
        return this.getData<LevelDataEventTriggerSpecialEvent>(LevelDataEventTriggerType.SpecialEvent);
    }

    @property({
        type :CCString,
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventTriggerType.Log ;
        }
    })
    public get message(): string {
        return this.logData.message;
    }
    public set message(value: string) {
        this.logData.message = value;
    }

    @property({visible: false})
    public _audio: AudioClip|null = null;
    @property({
        type :AudioClip,
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventTriggerType.Audio;
        }
    })
    public get audio(): AudioClip|null {
        return this._audio;
    }
    public set audio(value: AudioClip|null) {
        this._audio = value;
        if (value) {
            this.audioData.audioUUID = value.uuid;
        } else {
            this.audioData.audioUUID = "";
        }
    }

    @property({visible: false})
    public _waveGroup: LevelEditorWaveGroup[] = []; 
    @property({
        type: [LevelEditorWaveGroup],
        displayName: "波次组随机",
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventTriggerType.Wave;
        }
    })
    public get waveGroup(): LevelEditorWaveGroup[] {
        return this._waveGroup;
    }
    public set waveGroup(value: LevelEditorWaveGroup[]) {
        this._waveGroup = value;
        if (value) {
            this.waveData.waveGroup = [];
            value.forEach((waveGroup) => {
                let levelDataWaveGroup = new LevelDataEventWaveGroup();
                if (waveGroup.prefab) {
                    levelDataWaveGroup.wave.waveUUID = waveGroup.prefab.uuid;
                }
                levelDataWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
                levelDataWaveGroup.weight = waveGroup.weight;
                this.waveData.waveGroup.push(levelDataWaveGroup);
            });
        } else {
            this.waveData.waveGroup = [];
        }
    }

    public setWaveOffset(waveUUID: string, offset: Vec2) {
        for (let waveGroup of this.waveGroup) {
            if (waveGroup.prefab && waveGroup.prefab.uuid == waveUUID) {
                waveGroup.wave.waveOffset = offset;
                break;
            }
        }

        (this.data as LevelDataEventTriggerWave).waveGroup = [];
        this.waveGroup.forEach((waveGroup) => {
            let levelDataWaveGroup = new LevelDataEventWaveGroup();
            if (waveGroup.prefab) {
                levelDataWaveGroup.wave.waveUUID = waveGroup.prefab.uuid;
            }
            levelDataWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
            levelDataWaveGroup.weight = waveGroup.weight;
            (this.data as LevelDataEventTriggerWave).waveGroup.push(levelDataWaveGroup);
        });
    }

    @property({
        type: Enum(eLevelSpecialEvent),
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventTriggerType.SpecialEvent;
        }
    })
    public get eventType(): eLevelSpecialEvent {
        return this.specialEventData.eventType;
    }
    public set eventType(value: eLevelSpecialEvent) {
        this.specialEventData.eventType = value;
    }

    public copyFrom(source: LevelEditorEventTrigger) {
        this._index = source._index;
        this.dataType = source.dataType;
        switch (this.dataType) {
            case LevelDataEventTriggerType.Log:
                this.message = source.message;
                break;
            case LevelDataEventTriggerType.Audio:
                this.audio = source.audio;
                break;
            case LevelDataEventTriggerType.Wave:
                this.waveGroup = source.waveGroup.map((waveGroup) => {
                    let newWaveGroup = new LevelEditorWaveGroup();
                    newWaveGroup.wave.wavePrefab = waveGroup.wave.wavePrefab;
                    newWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
                    newWaveGroup.weight = waveGroup.weight;
                    return newWaveGroup;
                });
                break;
            case LevelDataEventTriggerType.SpecialEvent:
                this.eventType = source.eventType;
                break;
            default:
                break;
        }
    }

    public initByLevelData(data: LevelDataEventTrigger, index: number) {
        this._index = index;
        this.data = data;
        if (data._type == LevelDataEventTriggerType.Audio) {
            let uuid = (data as LevelDataEventTriggerAudio).audioUUID;
            if (uuid != "") {
                assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {
                    if (err) {
                        console.error("LevelEditorEventUI initByLevelData load audio err", err);
                        return;
                    }
                    this._audio = audio;
                });
            }
        }
        if (data._type == LevelDataEventTriggerType.Wave) {
            this._waveGroup.length = 0;
            let waveTrigger = data as LevelDataEventTriggerWave;
            for (let waveGroup of waveTrigger.waveGroup) { 
                let editorWaveGroup = new LevelEditorWaveGroup();
                // console.log('waveGroup.wave.waveUUID: ', waveGroup);
                assetManager.loadAny({uuid:waveGroup.wave.waveUUID}, (err, prefab:Prefab) => {
                    if (err) {
                        console.error("LevelEditorEventUI initByLevelData load wave prefab err", err);
                        return;
                    }
                    editorWaveGroup.wave.wavePrefab = prefab;
                });

                editorWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
                editorWaveGroup.weight = waveGroup.weight;
                this._waveGroup.push(editorWaveGroup);
            }
        }
    }
}

@ccclass('LevelEditorEventUI')
@executeInEditMode()
export class LevelEditorEventUI extends LevelEditorElemUI {
    @property([LevelEditorCondition])
    public conditions: LevelEditorCondition[] = [];
    @property([LevelEditorEventTrigger])
    public triggers: LevelEditorEventTrigger[] = [];

    private sprite: Sprite | null = null;
    onLoad() {
        this.sprite = this.node.getComponent(Sprite) || this.node.addComponent(Sprite);
        const eventSpriteUUID = 'aa8d57da-85f6-4319-8b4d-2a7864dccbe7';
        assetManager.loadAny<ImageAsset>(eventSpriteUUID, (err:Error|null, asset: ImageAsset) => {
            if (err) {
                console.warn(err);
                return;
            }

            this.sprite!.spriteFrame = SpriteFrame.createWithImage(asset);
            const uiTransform = this.node.getComponent(UITransform) || this.node.addComponent(UITransform);
            uiTransform.setContentSize(256, 256);
        });
    }

    // 标记是否修改过
    private _isDirty = false;
    private _isSelected = false;
    onFocusInEditor(): void {
        this._isDirty = true;
        this._isSelected = true;
    }

    onLostFocusInEditor(): void {
        this._isSelected = false;
    }
    public isSelected(): boolean {
        return this._isSelected;
    }

    private _cachedPosY: number = 0;
    private _waveMap: Map<string, Wave> = new Map();
    private _wavePreviews: WavePreview[] = [];
    public update(dt: number): void {
        for (let i = 0; i < this.conditions.length; i++) {
            const cond = this.conditions[i];
            cond._index = i;
            if (cond.dataType == LevelDataEventCondtionType.Wave 
                && (cond.data as LevelDataEventCondtionWave).targetElemID != "" 
                && cond._targetElem == null) {
                const elems = this.node.parent!.getComponentsInChildren(LevelEditorElemUI);
                for (let elem of elems) {
                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {
                        cond._targetElem = elem;
                        break;
                    }
                }
            }
        }

        if (this._isDirty) {
            this.syncWaveOffset();
            // clear wave childrens 
            this.node.removeAllChildren();
            this._waveMap.clear();

            // 重新创建波次预览节点
            this.triggers.forEach((trigger) => {
                trigger.waveGroup.forEach((waveGroup) => {
                    if (waveGroup.wave.wavePrefab) {
                        const prefab = waveGroup.wave.wavePrefab;
                        const waveNode = instantiate(prefab);
                        this.node.addChild(waveNode);
                        waveNode.setPosition(waveGroup.wave.waveOffset.x, waveGroup.wave.waveOffset.y);
                        this.setupWave(waveNode.getComponent(Wave));
                        this._waveMap.set(prefab.uuid, waveNode.getComponent(Wave)!);
                    }
                });
            });
        }

        if (!this._isPlaying && this._cachedPosY != this.node.position.y || this._isDirty) {
            this._cachedPosY = this.node.position.y;
            this.updateName();
            this._waveMap.forEach((wave) => {
                if (wave) {
                    wave.node.emit('event-tag', this.time.toFixed(2).toString());
                }
            });
        }

        if (this._isSelected) {
            // draw a rect with the size defined in GameConst
        }

        this._isDirty = false;
    }

    private syncWaveOffset() {
        if (this._waveMap.size > 0) {
            // 将位置修改同步回来
            this._waveMap.forEach((wave, key) => {
                if (!wave || !wave.node) return;

                const waveOffset = wave.node.position;
                for (let trigger of this.triggers) {
                    trigger.setWaveOffset(key, new Vec2(waveOffset.x, waveOffset.y));
                }
            });
        }
    }

    public initByLevelData(data: LevelDataEvent) {
        super.initByLevelData(data)
        if (data.conditions) {
            for (let i = 0; i < data.conditions.length; i++) {
                const condition = new LevelEditorCondition();
                condition._index = i;
                condition.data = data.conditions[i];
                this.conditions.push(condition);
            }
        }
        if (data.triggers) {
            for (let i = 0; i < data.triggers.length; i++) {
                const trigger = new LevelEditorEventTrigger();
                trigger.initByLevelData(data.triggers[i], i);
                this.triggers.push(trigger);
            }
        }

        this._isDirty = true;
    }

    public fillLevelData(data: LevelDataEvent) {
        super.fillLevelData(data)
        data.conditions = []
        this.conditions.forEach((cond) => {
            if (cond != null) {
                data.conditions.push(cond.data);
            }
        })
        this.syncWaveOffset();
        this.triggers.forEach((trigger) => {
            if (trigger != null) {
                data.triggers.push(trigger.data);
            }
        })
    }

    public copyFrom(source: LevelEditorEventUI) {
        this.elemID = source.elemID;
        this.remark = source.remark;
        this.layer = source.layer;
        this.node.position = source.node.position;
        this.conditions = source.conditions.map((cond) => {
            let newCond = new LevelEditorCondition();
            newCond.copyFrom(cond);
            return newCond;
        });;
        this.triggers = source.triggers.map((trigger) => {
            let newTrigger = new LevelEditorEventTrigger();
            newTrigger.copyFrom(trigger);
            return newTrigger;
        });;
    }

    public updateName() {
        const time = this.time;
        if (this.remark && this.remark != "") {
            this.node.name = this.remark + `_${time.toFixed(2)}`;
            return;
        }
        const layer = LevelLayerElemLayer[this.layer].toString();
        this.node.name = `${layer}_${time.toFixed(2)}`;
    }

    public setToSpecialEvent(eventType: eLevelSpecialEvent) {
        this.conditions = [];
        this.triggers = [];
        this.remark = eLevelSpecialEvent[eventType];
        if (eventType == eLevelSpecialEvent.SectionFinish) {
            // find last wave node
            const allEventNodes = this.node.parent!.getComponentsInChildren(LevelEditorEventUI);
            if (allEventNodes.length > 0) {
                // sort by position.y, the higher the y, the sooner it is in the array
                let waveNodes = allEventNodes.filter((node) => {
                    // filter [] by triggers which has wave trigger
                    const eventUI = node.getComponent(LevelEditorEventUI);
                    if (eventUI) {
                        for (let trigger of eventUI.triggers) {
                            if (trigger.data._type == LevelDataEventTriggerType.Wave) {
                                return true;
                            }
                        }
                    }
                    return false;
                }).sort((a, b) => {
                    return b.node.position.y - a.node.position.y;
                });

                if (waveNodes.length > 0) {
                    this.node.position = waveNodes[0].node.position;
                    let cond = new LevelEditorCondition();
                    cond.dataType = LevelDataEventCondtionType.Wave;
                    cond.targetElem = waveNodes[0];
                    this.conditions.push(cond);
                }
            }
        }
        let trigger = new LevelEditorEventTrigger();
        trigger.dataType = LevelDataEventTriggerType.SpecialEvent;
        trigger.eventType = eventType;
        this.triggers.push(trigger);
    }

    private setupWave(wave: Wave|null): void {
        if (wave == null) 
            return;

        wave.setupInEditor();
    }

    private _isPlaying = false;
    private _isActive = false;
    private _positionBeforePlay: Vec3 = new Vec3();
    public play(bPlay: boolean, progress: number) {
        if (bPlay != this._isPlaying) {
            this._isPlaying = bPlay;
            if (bPlay) {
                this.node.getPosition(this._positionBeforePlay);
            }
            else {
                this.node.setPosition(this._positionBeforePlay);
            }
        }

        if (bPlay) {
            const posY = this._positionBeforePlay.y - progress;
            if (posY <= GameConst.VIEWPORT_TOP) {
                if (!this._isActive) {
                    this.triggers.forEach((trigger) => {
                        this.performTrigger(trigger.data, posY);
                    })
                    this._isActive = true;
                }
            }
            this.node.setPosition(this._positionBeforePlay.x, posY, this._positionBeforePlay.z);
        }
        else {
            if (this._isActive) {
                this.resetPlay();
                this._isActive = false;
            }
        }
    }

    private resetPlay() {
        this._wavePreviews.forEach((wavePreview) => {
            wavePreview.clearPreview();
        });
        this._wavePreviews = [];
    }

    private performTrigger(trigger: LevelDataEventTrigger, offsetY: number) {
        switch (trigger._type) {
            case LevelDataEventTriggerType.Log:
                console.log("LevelEditorEventUI", "trigger log", (trigger as LevelDataEventTriggerLog).message);
                break;
            case LevelDataEventTriggerType.Audio:
                break;
            case LevelDataEventTriggerType.Wave:
                // Do Wave logic
                this.triggerWave(trigger as LevelDataEventTriggerWave, offsetY);
                break;
            default:
                break;
        }
    }

    private triggerWave(triggerWave: LevelDataEventTriggerWave, offsetY: number) {
        console.log('Trigger wave!! , ', offsetY);
        let totalWeight = 0;
        triggerWave.waveGroup.forEach((waveGroup) => {
            totalWeight += waveGroup.weight;
        });
        const randomWeight = Math.floor(Math.random() * totalWeight);

        let curWeight = 0;
        let selectedWaveGroup: LevelDataEventWaveGroup | null = null;
        for (let waveGroup of triggerWave.waveGroup) {
            curWeight += waveGroup.weight;
            if (randomWeight <= curWeight) {
                selectedWaveGroup = waveGroup;
                break;
            }
        }
        if (selectedWaveGroup == null) {
            console.error("LevelEditorEventUI triggerWave err, no selectedWaveGroup: ", triggerWave.waveGroup.length);
            return;
        }
        let wave = this._waveMap.get(selectedWaveGroup.wave.waveUUID);
        if (wave) {
            const position = this.node.position;
            const wavePreview = wave.node.getComponentInChildren(WavePreview);
            if (wavePreview) {
                wavePreview.triggerPreview(position.x, offsetY);
                this._wavePreviews.push(wavePreview);
            }
        } else {
            console.error("LevelEditorEventUI triggerWave err, no wave: ", selectedWaveGroup.wave.waveUUID);
        }

    }
}