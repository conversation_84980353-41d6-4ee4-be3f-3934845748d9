{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts"], "names": ["LoginInfo", "registerElem", "_decorator", "IMgr", "logDebug", "logError", "logInfo", "log<PERSON>arn", "<PERSON>", "csproto", "DataMgr", "ccclass", "NetStatus", "accountType", "code", "serverAddr", "constructor", "handler", "target", "eq", "NetMgr", "platformSDK", "_websocket", "_status", "NotConnect", "loginInfo", "_reconnectAttempts", "_maxReconnectAttempts", "_reconnectDelay", "_heartbeatInterval", "_heartbeatTimer", "_lastHeartbeatTime", "_messageHandlers", "Map", "_messageQueue", "_cmdSeq", "uninitRegistered", "unregister<PERSON><PERSON><PERSON>", "cs", "CS_CMD", "CS_CMD_GET_SESSION", "onGetSession", "CS_CMD_HEARTBEAT", "onHeartbeat", "CS_CMD_GET_ROLE", "onGetRole", "init", "registerHandler", "unInit", "disconnect", "clear", "length", "onUpdate", "dt", "Connected", "sendHeartbeat", "Date", "now", "handleDisconnection", "connect", "Connecting", "login", "err", "info", "createWebSocket", "close", "Disconnected", "getStatus", "isConnected", "WebSocket", "binaryType", "onopen", "onWebSocketOpen", "bind", "onmessage", "onWebSocketMessage", "onclose", "onWebSocketClose", "onerror", "onWebSocketError", "_event", "sendMessage", "get_session", "account_type", "platform", "PLATFORM", "PLATFORM_EDITOR", "version", "event", "buffer", "Uint8Array", "data", "handleMessage", "reason", "setTimeout", "processMessageQueue", "message", "shift", "sendRawMessage", "msg", "S2CMsg", "decode", "cmd", "JSON", "stringify", "dispatchMessage", "seq", "set", "handlers", "get", "for<PERSON>ach", "elem", "call", "stack", "decodeProtobufMessage", "_msgId", "msgId", "has", "push", "index", "findIndex", "splice", "delete", "C2SMsgBody", "create", "netMessage", "encodeProtobufMessage", "readyState", "OPEN", "buffD<PERSON>", "slice", "byteOffset", "byteLength", "send", "C2SMsg", "body", "clientData", "encode", "finish", "heartbeatData", "heartbeat", "clent_time", "fromNumber", "is_fighting", "clearAllHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_code", "comm", "RET_CODE", "RET_CODE_NO_ERROR", "sessionRsp", "openid", "uin_list", "get_role", "uin", "ZERO", "area_id", "roleRsp", "role", "setRole", "cmd_seq", "items", "item", "seq_no", "gameLogic", "checkGameID", "game_id", "pvp_status", "GAME_PVP_STATUS", "GAME_PVP_STATUS_NONE", "CS_CMD_GAME_PVP_GET_INFO", "game_pvp_get_info", "CS_CMD_GAME_PVP_GET_LIST", "game_pvp_get_list", "disableReconnect"], "mappings": ";;;+JA2BaA,S,EAMPC,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjCGC,MAAAA,U,OAAAA,U;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;AAC/BC,MAAAA,I;;AACAC,MAAAA,O;;AACEC,MAAAA,O,iBAAAA,O;;;;;;;;;OAGH;AAAEC,QAAAA;AAAF,O,GAAcT,U;;2BAERU,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;2BAiBCZ,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACnBa,WADmB;AAAA,eAEnBC,IAFmB;AAAA,eAGnBC,UAHmB;AAAA;;AAAA,O;;AAMjBd,MAAAA,Y,GAAN,MAAMA,YAAN,CAAmB;AAGfe,QAAAA,WAAW,CAACC,OAAD,EAA2BC,MAA3B,EAAwC;AAAA,eAFnDD,OAEmD;AAAA,eADnDC,MACmD;AAC/C,eAAKD,OAAL,GAAeA,OAAf;AACA,eAAKC,MAAL,GAAcA,MAAd;AACH;;AACDC,QAAAA,EAAE,CAACF,OAAD,EAA2BC,MAA3B,EAAiD;AAC/C,iBAAO,KAAKD,OAAL,KAAiBA,OAAjB,IAA4B,KAAKC,MAAL,KAAgBA,MAAnD;AACH;;AATc,O;;wBAaNE,M,WADZT,OAAO,CAAC,QAAD,C,iBAAR,MACaS,MADb;AAAA;AAAA,wBACiC;AAe7BJ,QAAAA,WAAW,CAACK,WAAD,EAA4B;AACnC;AADmC,eAd/BA,WAc+B,GAdI,IAcJ;AAAA,eAb/BC,UAa+B,GAbA,IAaA;AAAA,eAZ/BC,OAY+B,GAZVX,SAAS,CAACY,UAYA;AAAA,eAX/BC,SAW+B,GAXD,IAWC;AAAA,eAV/BC,kBAU+B,GAVF,CAUE;AAAA,eAT/BC,qBAS+B,GATC,CASD;AAAA,eAR/BC,eAQ+B,GARL,IAQK;AARC;AAQD,eAP/BC,kBAO+B,GAPF,IAOE;AAPI;AAOJ,eAN/BC,eAM+B,GANL,CAMK;AAAA,eAL/BC,kBAK+B,GALF,CAKE;AAAA,eAJ/BC,gBAI+B,GAJ4B,IAAIC,GAAJ,EAI5B;AAAA,eAH/BC,aAG+B,GAHA,EAGA;AAAA,eAF/BC,OAE+B,GAFW,IAAIF,GAAJ,EAEX;AAEnC,eAAKZ,WAAL,GAAmBA,WAAnB;AACH;;AAEOe,QAAAA,gBAAgB,GAAS;AAC7B,eAAKC,iBAAL,CAAuB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,kBAAzC,EAA6D,KAAKC,YAAlE,EAAgF,IAAhF;AACA,eAAKJ,iBAAL,CAAuB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,gBAAzC,EAA2D,KAAKC,WAAhE,EAA6E,IAA7E;AACA,eAAKN,iBAAL,CAAuB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,eAAzC,EAA0D,KAAKC,SAA/D,EAA0E,IAA1E;AACH;;AAEDC,QAAAA,IAAI,GAAS;AACT;AAAA;AAAA,kCAAQ,QAAR,EAAkB,6BAAlB;AAEA,eAAKC,eAAL,CAAqB;AAAA;AAAA,kCAAQT,EAAR,CAAWC,MAAX,CAAkBC,kBAAvC,EAA2D,KAAKC,YAAhE,EAA8E,IAA9E;AACA,eAAKM,eAAL,CAAqB;AAAA;AAAA,kCAAQT,EAAR,CAAWC,MAAX,CAAkBG,gBAAvC,EAAyD,KAAKC,WAA9D,EAA2E,IAA3E;AACA,eAAKI,eAAL,CAAqB;AAAA;AAAA,kCAAQT,EAAR,CAAWC,MAAX,CAAkBK,eAAvC,EAAwD,KAAKC,SAA7D,EAAwE,IAAxE;AACH;;AAEDG,QAAAA,MAAM,GAAS;AACX,eAAKZ,gBAAL;AAEA,eAAKa,UAAL;;AACA,eAAKjB,gBAAL,CAAsBkB,KAAtB;;AACA,eAAKhB,aAAL,CAAmBiB,MAAnB,GAA4B,CAA5B;AACA,gBAAMH,MAAN;AACH;;AAEDI,QAAAA,QAAQ,CAACC,EAAD,EAAmB;AACvB,eAAKvB,eAAL,IAAwBuB,EAAE,GAAG,IAA7B,CADuB,CAGvB;;AACA,cAAI,KAAK9B,OAAL,KAAiBX,SAAS,CAAC0C,SAA3B,IACA,KAAKxB,eAAL,IAAwB,KAAKD,kBADjC,EACqD;AACjD,iBAAK0B,aAAL;AACA,iBAAKzB,eAAL,GAAuB,CAAvB;AACH,WARsB,CAUvB;;;AACA,cAAI,KAAKP,OAAL,KAAiBX,SAAS,CAAC0C,SAA3B,IACAE,IAAI,CAACC,GAAL,KAAa,KAAK1B,kBAAlB,GAAuC,KAAKF,kBAAL,GAA0B,CADrE,EACwE;AACpE;AAAA;AAAA,oCAAQ,QAAR,EAAkB,6CAAlB;AACA,iBAAK6B,mBAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,OAAO,GAAS;AAAA;;AACZ,cAAI,KAAKpC,OAAL,KAAiBX,SAAS,CAACgD,UAA3B,IAAyC,KAAKrC,OAAL,KAAiBX,SAAS,CAAC0C,SAAxE,EAAmF;AAC/E;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH;;AACF,eAAK/B,OAAL,GAAeX,SAAS,CAACgD,UAAzB;AACA,eAAKlC,kBAAL,GAA0B,CAA1B;AACA,oCAAKL,WAAL,+BAAkBwC,KAAlB,CAAwB,CAACC,GAAD,EAAMC,IAAN,KAAe;AAAA;;AACnC,gBAAID,GAAJ,EAAS;AACL;AAAA;AAAA,wCAAS,QAAT,oBAAmCA,GAAnC;AACA;AACH;;AACD,iBAAKrC,SAAL,GAAiBsC,IAAjB;AACA;AAAA;AAAA,oCAAQ,QAAR,yCAAmC,KAAKtC,SAAxC,qBAAmC,gBAAgBV,UAAnD;AACA,iBAAKiD,eAAL;AACH,WARD;AAUF;;AAEDH,QAAAA,KAAK,CAACE,IAAD,EAAwB;AACzB,eAAKtC,SAAL,GAAiBsC,IAAjB;AACA,eAAKJ,OAAL;AACH;AAED;AACJ;AACA;;;AACIV,QAAAA,UAAU,GAAS;AACf,cAAI,KAAK3B,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB2C,KAAhB;;AACA,iBAAK3C,UAAL,GAAkB,IAAlB;AACH;;AACD,eAAKC,OAAL,GAAeX,SAAS,CAACsD,YAAzB;AACA,eAAKxC,kBAAL,GAA0B,CAA1B;AACA;AAAA;AAAA,kCAAQ,QAAR,EAAkB,0BAAlB;AACH;AAED;AACJ;AACA;;;AACIyC,QAAAA,SAAS,GAAc;AACnB,iBAAO,KAAK5C,OAAZ;AACH;AAED;AACJ;AACA;;;AACI6C,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAK7C,OAAL,KAAiBX,SAAS,CAAC0C,SAAlC;AACH;AAED;AACJ;AACA;;;AACYU,QAAAA,eAAe,GAAS;AAC5B,cAAI;AACA,iBAAK1C,UAAL,GAAkB,IAAI+C,SAAJ,CAAc,KAAK5C,SAAL,CAAgBV,UAA9B,CAAlB;AACA,iBAAKO,UAAL,CAAgBgD,UAAhB,GAA6B,aAA7B;AAEA,iBAAKhD,UAAL,CAAgBiD,MAAhB,GAAyB,KAAKC,eAAL,CAAqBC,IAArB,CAA0B,IAA1B,CAAzB;AACA,iBAAKnD,UAAL,CAAgBoD,SAAhB,GAA4B,KAAKC,kBAAL,CAAwBF,IAAxB,CAA6B,IAA7B,CAA5B;AACA,iBAAKnD,UAAL,CAAgBsD,OAAhB,GAA0B,KAAKC,gBAAL,CAAsBJ,IAAtB,CAA2B,IAA3B,CAA1B;AACA,iBAAKnD,UAAL,CAAgBwD,OAAhB,GAA0B,KAAKC,gBAAL,CAAsBN,IAAtB,CAA2B,IAA3B,CAA1B;AAEH,WATD,CASE,OAAOX,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,mCAAkDA,GAAlD;AACA,iBAAKJ,mBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYc,QAAAA,eAAe,CAACQ,MAAD,EAAsB;AACzC;AAAA;AAAA,kCAAQ,QAAR,EAAkB,qBAAlB;AACA,eAAKzD,OAAL,GAAeX,SAAS,CAAC0C,SAAzB;AACA,eAAK5B,kBAAL,GAA0B,CAA1B;AACA,eAAKK,kBAAL,GAA0ByB,IAAI,CAACC,GAAL,EAA1B;AAEA,eAAKwB,WAAL,CAAiB;AAAA;AAAA,kCAAQ3C,EAAR,CAAWC,MAAX,CAAkBC,kBAAnC,EAAuD;AACnD0C,YAAAA,WAAW,EAAE;AACTC,cAAAA,YAAY,EAAE,KAAK1D,SAAL,CAAgBZ,WADrB;AACkC;AAC3CuE,cAAAA,QAAQ,EAAE;AAAA;AAAA,sCAAQ9C,EAAR,CAAW+C,QAAX,CAAoBC,eAFrB;AAEsC;AAC/CxE,cAAAA,IAAI,EAAE,KAAKW,SAAL,CAAgBX,IAHb;AAGmB;AAC5ByE,cAAAA,OAAO,EAAE,CAJA,CAIG;;AAJH;AADsC,WAAvD;AASH;AAED;AACJ;AACA;;;AACYZ,QAAAA,kBAAkB,CAACa,KAAD,EAA4B;AAClD,cAAI;AACA;AAAA;AAAA,sCAAS,QAAT,kCAAiDA,KAAjD;AACA,gBAAMC,MAAM,GAAG,IAAIC,UAAJ,CAAeF,KAAK,CAACG,IAArB,CAAf;AACA,iBAAKC,aAAL,CAAmBH,MAAnB;AACA,iBAAK1D,kBAAL,GAA0ByB,IAAI,CAACC,GAAL,EAA1B;AACH,WALD,CAKE,OAAOK,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,iCAAgDA,GAAhD;AACH;AACJ;AAED;AACJ;AACA;;;AACYe,QAAAA,gBAAgB,CAACW,KAAD,EAA0B;AAC9C;AAAA;AAAA,kCAAQ,QAAR,yBAAuCA,KAAK,CAAC1E,IAA7C,WAAuD0E,KAAK,CAACK,MAA7D;AACA,eAAKnC,mBAAL;AACH;AAED;AACJ;AACA;;;AACYqB,QAAAA,gBAAgB,CAACC,MAAD,EAAsB;AAC1C;AAAA;AAAA,oCAAS,QAAT,EAAmB,0BAAnB;AACA,eAAKtB,mBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,mBAAmB,GAAS;AAChC,cAAI,KAAKpC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB2C,KAAhB;;AACA,iBAAK3C,UAAL,GAAkB,IAAlB;AACH;;AAED,eAAKC,OAAL,GAAeX,SAAS,CAACY,UAAzB,CANgC,CAQhC;;AACA,cAAI,KAAKE,kBAAL,GAA0B,KAAKC,qBAAnC,EAA0D;AACtD,iBAAKD,kBAAL;AACA;AAAA;AAAA,oCAAQ,QAAR,+BAA6C,KAAKA,kBAAlD,SAAwE,KAAKC,qBAA7E;AAEAmE,YAAAA,UAAU,CAAC,MAAM;AACb,kBAAI,KAAKrE,SAAL,CAAgBV,UAAhB,IAA8B,KAAKQ,OAAL,KAAiBX,SAAS,CAACsD,YAA7D,EAA2E;AACvE,qBAAKP,OAAL;AACH;AACJ,aAJS,EAIP,KAAK/B,eAJE,CAAV;AAKH,WATD,MASO;AACH;AAAA;AAAA,sCAAS,QAAT,EAAmB,mCAAnB;AACA,iBAAKL,OAAL,GAAeX,SAAS,CAACsD,YAAzB;AACH;AACJ;AAED;AACJ;AACA;;;AACY6B,QAAAA,mBAAmB,GAAS;AAChC,iBAAO,KAAK7D,aAAL,CAAmBiB,MAAnB,GAA4B,CAA5B,IAAiC,KAAKiB,WAAL,EAAxC,EAA4D;AACxD,gBAAM4B,OAAO,GAAG,KAAK9D,aAAL,CAAmB+D,KAAnB,EAAhB;;AACA,gBAAID,OAAJ,EAAa;AACT,mBAAKE,cAAL,CAAoBF,OAApB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYJ,QAAAA,aAAa,CAACH,MAAD,EAA2B;AAC5C,cAAI;AACA;AACA,gBAAIU,GAAG,GAAG;AAAA;AAAA,oCAAQ7D,EAAR,CAAW8D,MAAX,CAAkBC,MAAlB,CAAyBZ,MAAzB,CAAV;;AACA,gBAAIU,GAAG,CAACG,GAAJ,IAAW;AAAA;AAAA,oCAAQhE,EAAR,CAAWC,MAAX,CAAkBG,gBAAjC,EAAmD;AAC/C;AAAA;AAAA,sCAAQ,QAAR,wBAAsC;AAAA;AAAA,sCAAQJ,EAAR,CAAWC,MAAX,CAAkB4D,GAAG,CAACG,GAAtB,CAAtC,SAAqEC,IAAI,CAACC,SAAL,CAAeL,GAAf,CAArE;AACH;;AACD,iBAAKM,eAAL,CAAqBN,GAArB;AAEH,WARD,CAQE,OAAOrC,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,gCAA+CA,GAA/C;AACH;AACJ;AAED;AACJ;AACA;;;AACY2C,QAAAA,eAAe,CAACN,GAAD,EAAgC;AACnD,cAAI,CAAC,CAACA,GAAG,CAACO,GAAV,EAAe;AACX,iBAAKvE,OAAL,CAAawE,GAAb,CAAiBR,GAAG,CAACG,GAArB,EAA2BH,GAAG,CAACO,GAA/B;AACH;;AACD,cAAME,QAAQ,GAAG,KAAK5E,gBAAL,CAAsB6E,GAAtB,CAA0BV,GAAG,CAACG,GAA9B,CAAjB;;AACA,cAAIM,QAAQ,IAAIA,QAAQ,CAACzD,MAAT,GAAkB,CAAlC,EAAqC;AACjC,gBAAI;AACAyD,cAAAA,QAAQ,CAACE,OAAT,CAAiBC,IAAI,IAAI;AACrB,oBAAI;AACAA,kBAAAA,IAAI,CAAC9F,OAAL,CAAa+F,IAAb,CAAkBD,IAAI,CAAC7F,MAAvB,EAA+BiF,GAA/B;AACH,iBAFD,CAEE,OAAOrC,GAAP,EAAY;AACV;AAAA;AAAA,4CAAS,QAAT,+BAA8C;AAAA;AAAA,0CAAQxB,EAAR,CAAWC,MAAX,CAAkB4D,GAAG,CAACG,GAAtB,CAA9C,UAA+ExC,GAAD,CAAemD,KAA7F;AACH;AACJ,eAND;AAOH,aARD,CAQE,OAAOnD,GAAP,EAAY;AACV;AAAA;AAAA,wCAAS,QAAT,gCAA+C;AAAA;AAAA,sCAAQxB,EAAR,CAAWC,MAAX,CAAkB4D,GAAG,CAACG,GAAtB,CAA/C,UAAgFxC,GAAD,CAAemD,KAA9F;AACH;AACJ,WAZD,MAYO;AACH;AAAA;AAAA,oCAAQ,QAAR,wCAAsD;AAAA;AAAA,oCAAQ3E,EAAR,CAAWC,MAAX,CAAkB4D,GAAG,CAACG,GAAtB,CAAtD;AACH;AACJ;AAED;AACJ;AACA;;;AACYY,QAAAA,qBAAqB,CAACC,MAAD,EAAiBxB,IAAjB,EAAwC;AACjE;AACA;AACA,iBAAOA,IAAP;AACH;AAED;AACJ;AACA;;;AACI5C,QAAAA,eAAe,CAACqE,KAAD,EAA2BnG,OAA3B,EAAqDC,MAArD,EAAwE;AACnF,cAAI,CAAC,KAAKc,gBAAL,CAAsBqF,GAAtB,CAA0BD,KAA1B,CAAL,EAAuC;AACnC,iBAAKpF,gBAAL,CAAsB2E,GAAtB,CAA0BS,KAA1B,EAAiC,EAAjC;AACH;;AACD,eAAKpF,gBAAL,CAAsB6E,GAAtB,CAA0BO,KAA1B,EAAkCE,IAAlC,CAAuC,IAAIrH,YAAJ,CAAiBgB,OAAjB,EAA0BC,MAA1B,CAAvC;;AACA;AAAA;AAAA,kCAAQ,QAAR,qCAAmD;AAAA;AAAA,kCAAQoB,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAAnD;AACH;AAED;AACJ;AACA;;;AACI/E,QAAAA,iBAAiB,CAAC+E,KAAD,EAA2BnG,OAA3B,EAAqDC,MAArD,EAAwE;AACrF,cAAM0F,QAAQ,GAAG,KAAK5E,gBAAL,CAAsB6E,GAAtB,CAA0BO,KAA1B,CAAjB;;AACA,cAAIR,QAAJ,EAAc;AACV,gBAAMW,KAAK,GAAGX,QAAQ,CAACY,SAAT,CAAoBT,IAAD,IAAU;AACvC,qBAAOA,IAAI,CAAC5F,EAAL,CAAQF,OAAR,EAAiBC,MAAjB,CAAP;AACH,aAFa,CAAd;;AAGA,gBAAIqG,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdX,cAAAA,QAAQ,CAACa,MAAT,CAAgBF,KAAhB,EAAuB,CAAvB;AACA;AAAA;AAAA,sCAAQ,QAAR,uCAAqD;AAAA;AAAA,sCAAQjF,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAArD,EAFc,CAId;;AACA,kBAAIR,QAAQ,CAACzD,MAAT,KAAoB,CAAxB,EAA2B;AACvB,qBAAKnB,gBAAL,CAAsB0F,MAAtB,CAA6BN,KAA7B;;AACA;AAAA;AAAA,wCAAQ,QAAR,8CAA4D;AAAA;AAAA,wCAAQ9E,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAA5D;AACH;AACJ,aATD,MASO;AACH;AAAA;AAAA,sCAAQ,QAAR,oCAAkD;AAAA;AAAA,sCAAQ9E,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAAlD;AACH;AACJ,WAhBD,MAgBO;AACH;AAAA;AAAA,oCAAQ,QAAR,yCAAuD;AAAA;AAAA,oCAAQ9E,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAAvD;AACH;AACJ;AAED;AACJ;AACA;;;AACInC,QAAAA,WAAW,CAACmC,KAAD,EAA2BpB,OAA3B,EAAkE;AACzE,cAAIoB,KAAK,IAAI;AAAA;AAAA,kCAAQ9E,EAAR,CAAWC,MAAX,CAAkBG,gBAA/B,EAAiD;AAC7C;AAAA;AAAA,oCAAQ,QAAR,mBAAiC;AAAA;AAAA,oCAAQJ,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAAjC,SAA6Db,IAAI,CAACC,SAAL,CAAe;AAAA;AAAA,oCAAQlE,EAAR,CAAWqF,UAAX,CAAsBC,MAAtB,CAA6B5B,OAA7B,CAAf,CAA7D;AACH;;AACD,cAAI;AACA;AACA,gBAAM6B,UAAU,GAAG,KAAKC,qBAAL,CAA2BV,KAA3B,EAAkCpB,OAAlC,CAAnB;AACA;AAAA;AAAA,sCAAS,QAAT,mBAAkC;AAAA;AAAA,oCAAQ1D,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAAlC,aAAkES,UAAU,CAACnB,GAA7E;;AAEA,gBAAI,KAAKtC,WAAL,EAAJ,EAAwB;AACpB,mBAAK8B,cAAL,CAAoB2B,UAApB;AACH,aAFD,MAEO;AACH;AACA,mBAAK3F,aAAL,CAAmBoF,IAAnB,CAAwBO,UAAxB;;AACA;AAAA;AAAA,sCAAQ,QAAR,sBAAoC;AAAA;AAAA,sCAAQvF,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAApC,aAAoES,UAAU,CAACnB,GAA/E;AACH;AACJ,WAZD,CAYE,OAAO5C,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,8BAA6C;AAAA;AAAA,oCAAQxB,EAAR,CAAWC,MAAX,CAAkB6E,KAAlB,CAA7C,UAA0EtD,GAA1E;AACH;AACJ;AAED;AACJ;AACA;;;AACYoC,QAAAA,cAAc,CAACF,OAAD,EAA6B;AAC/C,cAAI,CAAC,KAAK1E,UAAN,IAAoB,KAAKA,UAAL,CAAgByG,UAAhB,KAA+B1D,SAAS,CAAC2D,IAAjE,EAAuE;AACnE;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH;;AAED,cAAI;AACA,gBAAIC,QAAQ,GAAGjC,OAAO,CAACL,IAAR,CAAaF,MAAb,CAAoByC,KAApB,CAA0BlC,OAAO,CAACL,IAAR,CAAawC,UAAvC,EAAmDnC,OAAO,CAACL,IAAR,CAAawC,UAAb,GAAwBnC,OAAO,CAACL,IAAR,CAAayC,UAAxF,CAAf;;AACA,iBAAK9G,UAAL,CAAgB+G,IAAhB,CAAqBJ,QAArB;;AACA;AAAA;AAAA,sCAAS,QAAT,oBAAmC;AAAA;AAAA,oCAAQ3F,EAAR,CAAWC,MAAX,CAAkByD,OAAO,CAACoB,KAA1B,CAAnC,gBAA8EpB,OAAO,CAACL,IAAR,CAAayC,UAA3F;AAEH,WALD,CAKE,OAAOtE,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,mCAAkDA,GAAlD;AACH;AACJ;AAED;AACJ;AACA;;;AACYgE,QAAAA,qBAAqB,CAACX,MAAD,EAA4BnB,OAA5B,EAA0E;AAAA;;AACnG;AACA;AAEA,cAAIU,GAAG,wBAAG,KAAKvE,OAAL,CAAa0E,GAAb,CAAiBM,MAAjB,CAAH,gCAA+B,CAAtC;;AACA,cAAInB,OAAO,YAAYN,UAAvB,EAAmC;AAC/B,gBAAMmC,WAAuB,GAAG;AAC5BT,cAAAA,KAAK,EAAED,MADqB;AAE5BT,cAAAA,GAAG,EAAEA,GAFuB;AAG5Bf,cAAAA,IAAI,EAAEK;AAHsB,aAAhC;AAKA,mBAAO6B,WAAP;AACH;;AAED,cAAI1B,GAAG,GAAG,IAAI;AAAA;AAAA,kCAAQ7D,EAAR,CAAWgG,MAAf,EAAV;AACAnC,UAAAA,GAAG,CAACG,GAAJ,GAAUa,MAAV;AACAhB,UAAAA,GAAG,CAACO,GAAJ,GAAUA,GAAV;AACAP,UAAAA,GAAG,CAACoC,IAAJ,GAAWvC,OAAX;AAEA,cAAMwC,UAAU,GAAG;AAAA;AAAA,kCAAQlG,EAAR,CAAWgG,MAAX,CAAkBG,MAAlB,CAAyBtC,GAAzB,EAA8BuC,MAA9B,EAAnB;AACA,cAAMb,UAAuB,GAAG;AAC5BT,YAAAA,KAAK,EAAED,MADqB;AAE5BT,YAAAA,GAAG,EAAEA,GAFuB;AAG5Bf,YAAAA,IAAI,EAAE6C;AAHsB,WAAhC;AAKA,iBAAOX,UAAP;AACH;AAED;AACJ;AACA;;;AACYtE,QAAAA,aAAa,GAAS;AAC1B;AACA,cAAMoF,aAAqC,GAAG;AAC1CC,YAAAA,SAAS,EAAE;AACPC,cAAAA,UAAU,EAAE;AAAA;AAAA,gCAAKC,UAAL,CAAgBtF,IAAI,CAACC,GAAL,EAAhB,CADL;AACkC;AACzCsF,cAAAA,WAAW,EAAE,CAFN,CAES;;AAFT;AAD+B,WAA9C;AAMA,eAAK9D,WAAL,CAAiB;AAAA;AAAA,kCAAQ3C,EAAR,CAAWC,MAAX,CAAkBG,gBAAnC,EAAqDiG,aAArD;AACH;AAED;AACJ;AACA;;;AACIK,QAAAA,gBAAgB,GAAS;AACrB,eAAKhH,gBAAL,CAAsBkB,KAAtB;;AACA;AAAA;AAAA,kCAAQ,QAAR,EAAkB,8BAAlB;AACH;AAED;AACJ;AACA;;;AACI+F,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAK/G,aAAL,CAAmBiB,MAA1B;AACH;;AAEDR,QAAAA,WAAW,CAACwD,GAAD,EAAgC;AACvC;AAAA;AAAA,oCAAS,QAAT,mBAAkCA,GAAlC;AACH;;AAED1D,QAAAA,YAAY,CAAC0D,GAAD,EAAgC;AAAA;;AACxC;AAAA;AAAA,oCAAS,QAAT,oBAAmCA,GAAnC;;AACA,cAAIA,GAAG,CAAC+C,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,2BAAyClD,GAAG,CAAC+C,QAA7C;AACA;AACH;;AACD,cAAII,UAAU,gBAAGnD,GAAG,CAACoC,IAAP,qBAAG,UAAUrD,WAA3B;;AACA,cAAI,CAACoE,UAAL,EAAiB;AACb;AAAA;AAAA,oCAAQ,QAAR,EAAkB,2BAAlB;AACA;AACH;;AACD;AAAA;AAAA,kCAAQ,QAAR,oBAAkCA,UAAU,CAACC,MAA7C,SAAuDD,UAAU,CAACE,QAAlE;AACA,eAAKvE,WAAL,CAAiB;AAAA;AAAA,kCAAQ3C,EAAR,CAAWC,MAAX,CAAkBK,eAAnC,EAAoD;AAChD6G,YAAAA,QAAQ,EAAE;AACNC,cAAAA,GAAG,EAAE;AAAA;AAAA,gCAAKC,IADJ;AAENC,cAAAA,OAAO,EAAE;AAFH;AADsC,WAApD;AAMH;;AAED/G,QAAAA,SAAS,CAACsD,GAAD,EAAgC;AAAA;;AACrC,cAAIA,GAAG,CAAC+C,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,wBAAsClD,GAAG,CAAC+C,QAA1C;AACA;AACH;;AACD,cAAIW,OAAO,iBAAG1D,GAAG,CAACoC,IAAP,qBAAG,WAAUkB,QAAxB;;AACA,cAAI,CAACI,OAAL,EAAc;AACV;AAAA;AAAA,oCAAQ,QAAR,EAAkB,wBAAlB;AACA;AACH;;AACD;AAAA;AAAA,kCAAQ/G,IAAR;AACA;AAAA;AAAA,kCAAQgH,IAAR,CAAaC,OAAb,CAAqBF,OAArB;;AACA,kCAAIA,OAAO,CAACG,OAAZ,iCAAI,iBAAiBC,KAArB,aAAI,iBAAwBnD,OAAxB,CAAiCoD,IAAD,IAAU;AAC1C,iBAAK/H,OAAL,CAAawE,GAAb,CAAiBuD,IAAI,CAAC5D,GAAtB,EAA4B4D,IAAI,CAACC,MAAjC;AACH,WAFG,CAAJ,EAEI;AACA;AAAA;AAAA,oCAAQ,QAAR,yBAAuC5D,IAAI,CAACC,SAAL,CAAeqD,OAAO,CAACG,OAAR,CAAgBC,KAA/B,CAAvC;AACH;;AACD;AAAA;AAAA,kCAAQG,SAAR,CAAkBC,WAAlB,CAA8BR,OAAO,CAAES,OAAvC;;AACA,cAAIT,OAAO,CAACU,UAAR,IAAsB;AAAA;AAAA,kCAAQpB,IAAR,CAAaqB,eAAb,CAA6BC,oBAAvD,EAA6E;AACzE,iBAAKxF,WAAL,CAAiB;AAAA;AAAA,oCAAQ3C,EAAR,CAAWC,MAAX,CAAkBmI,wBAAnC,EAA6D;AAAEC,cAAAA,iBAAiB,EAAE;AAArB,aAA7D;AACH;;AACD,eAAK1F,WAAL,CAAiB;AAAA;AAAA,kCAAQ3C,EAAR,CAAWC,MAAX,CAAkBqI,wBAAnC,EAA6D;AAAEC,YAAAA,iBAAiB,EAAE;AAArB,WAA7D;AACH;;AACDC,QAAAA,gBAAgB,GAAS;AACrB,eAAKpJ,kBAAL,GAA0B,KAAKC,qBAA/B;AACH;;AAhd4B,O", "sourcesContent": ["import { _decorator } from \"cc\";\r\nimport { IMgr } from 'db://assets/scripts/core/base/IMgr';\r\nimport { logDebug, logError, logInfo, logWarn } from 'db://assets/scripts/utils/Logger';\r\nimport Long from 'long';\r\nimport csproto from '../autogen/pb/cs_proto.js';\r\nimport { DataMgr } from \"../data/DataManager\";\r\nimport { GameIns } from \"../game/GameIns\";\r\nimport { IPlatformSDK } from \"../platformsdk/IPlatformSDK.js\";\r\nconst { ccclass } = _decorator;\r\n\r\nexport enum NetStatus {\r\n    NotConnect = 0,\r\n    Connecting = 1,\r\n    ServerPassed = 2,\r\n    Disconnected = 3,    // 服务器或客户端主动断开, 该状态不会自动重连\r\n    Connected = 4,\r\n}\r\n\r\nexport interface INetMessage {\r\n    msgId: csproto.cs.CS_CMD;\r\n    data: Uint8Array;\r\n    seq?: number;\r\n}\r\n\r\nexport interface IMessageHandler {\r\n    (data: csproto.cs.IS2CMsg): void;\r\n}\r\nexport class LoginInfo {\r\n    accountType!: csproto.cs.ACCOUNT_TYPE;\r\n    code!: string;\r\n    serverAddr!: string;\r\n}\r\n\r\nclass registerElem {\r\n    handler: IMessageHandler;\r\n    target: any;\r\n    constructor(handler: IMessageHandler, target: any) {\r\n        this.handler = handler;\r\n        this.target = target;\r\n    }\r\n    eq(handler: IMessageHandler, target: any): boolean {\r\n        return this.handler === handler && this.target === target;\r\n    }\r\n}\r\n\r\n@ccclass(\"NetMgr\")\r\nexport class NetMgr extends IMgr {\r\n    private platformSDK: IPlatformSDK | null = null;\r\n    private _websocket: WebSocket | null = null;\r\n    private _status: NetStatus = NetStatus.NotConnect;\r\n    private loginInfo: LoginInfo | null = null;\r\n    private _reconnectAttempts: number = 0;\r\n    private _maxReconnectAttempts: number = 5;\r\n    private _reconnectDelay: number = 3000; // 3 seconds\r\n    private _heartbeatInterval: number = 3000; // 30 seconds\r\n    private _heartbeatTimer: number = 0;\r\n    private _lastHeartbeatTime: number = 0;\r\n    private _messageHandlers: Map<csproto.cs.CS_CMD, registerElem[]> = new Map();\r\n    private _messageQueue: INetMessage[] = [];\r\n    private _cmdSeq: Map<csproto.cs.CS_CMD, number> = new Map();\r\n\r\n    constructor(platformSDK: IPlatformSDK) {\r\n        super();\r\n        this.platformSDK = platformSDK;\r\n    }\r\n\r\n    private uninitRegistered(): void {\r\n        this.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_SESSION, this.onGetSession, this);\r\n        this.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_HEARTBEAT, this.onHeartbeat, this);\r\n        this.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this);\r\n    }\r\n\r\n    init(): void {\r\n        logInfo(\"NetMgr\", \"Network manager initialized\");\r\n\r\n        this.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_SESSION, this.onGetSession, this);\r\n        this.registerHandler(csproto.cs.CS_CMD.CS_CMD_HEARTBEAT, this.onHeartbeat, this);\r\n        this.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this);\r\n    }\r\n\r\n    unInit(): void {\r\n        this.uninitRegistered()\r\n\r\n        this.disconnect();\r\n        this._messageHandlers.clear();\r\n        this._messageQueue.length = 0;\r\n        super.unInit();\r\n    }\r\n\r\n    onUpdate(dt: number): void {\r\n        this._heartbeatTimer += dt * 1000;\r\n\r\n        // Send heartbeat\r\n        if (this._status === NetStatus.Connected &&\r\n            this._heartbeatTimer >= this._heartbeatInterval) {\r\n            this.sendHeartbeat();\r\n            this._heartbeatTimer = 0;\r\n        }\r\n\r\n        // Check connection timeout\r\n        if (this._status === NetStatus.Connected &&\r\n            Date.now() - this._lastHeartbeatTime > this._heartbeatInterval * 2) {\r\n            logWarn(\"NetMgr\", \"Connection timeout, attempting to reconnect\");\r\n            this.handleDisconnection();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect to server\r\n     * @param url WebSocket server URL\r\n     */\r\n    connect(): void {\r\n        if (this._status === NetStatus.Connecting || this._status === NetStatus.Connected) {\r\n            logWarn(\"NetMgr\", \"Already connecting or connected\");\r\n            return;\r\n        }\r\n       this._status = NetStatus.Connecting;\r\n       this._reconnectAttempts = 0;\r\n       this.platformSDK?.login((err, info) => {\r\n           if (err) {\r\n               logError(\"NetMgr\", `login failed ${err}`);\r\n               return;\r\n           }\r\n           this.loginInfo = info;\r\n           logInfo(\"NetMgr\", `Connecting to ${this.loginInfo?.serverAddr}`);\r\n           this.createWebSocket();\r\n       })\r\n\r\n    }\r\n\r\n    login(info: LoginInfo): void {\r\n        this.loginInfo = info;\r\n        this.connect();\r\n    }\r\n\r\n    /**\r\n     * Disconnect from server\r\n     */\r\n    disconnect(): void {\r\n        if (this._websocket) {\r\n            this._websocket.close();\r\n            this._websocket = null;\r\n        }\r\n        this._status = NetStatus.Disconnected;\r\n        this._reconnectAttempts = 0;\r\n        logInfo(\"NetMgr\", \"Disconnected from server\");\r\n    }\r\n\r\n    /**\r\n     * Get current connection status\r\n     */\r\n    getStatus(): NetStatus {\r\n        return this._status;\r\n    }\r\n\r\n    /**\r\n     * Check if connected\r\n     */\r\n    isConnected(): boolean {\r\n        return this._status === NetStatus.Connected;\r\n    }\r\n\r\n    /**\r\n     * Create WebSocket connection\r\n     */\r\n    private createWebSocket(): void {\r\n        try {\r\n            this._websocket = new WebSocket(this.loginInfo!.serverAddr);\r\n            this._websocket.binaryType = 'arraybuffer';\r\n\r\n            this._websocket.onopen = this.onWebSocketOpen.bind(this);\r\n            this._websocket.onmessage = this.onWebSocketMessage.bind(this);\r\n            this._websocket.onclose = this.onWebSocketClose.bind(this);\r\n            this._websocket.onerror = this.onWebSocketError.bind(this);\r\n\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to create WebSocket: ${err}`);\r\n            this.handleDisconnection();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * WebSocket open event handler\r\n     */\r\n    private onWebSocketOpen(_event: Event): void {\r\n        logInfo(\"NetMgr\", \"WebSocket connected\");\r\n        this._status = NetStatus.Connected;\r\n        this._reconnectAttempts = 0;\r\n        this._lastHeartbeatTime = Date.now();\r\n\r\n        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_SESSION, {\r\n            get_session: {\r\n                account_type: this.loginInfo!.accountType, // 账号类型\r\n                platform: csproto.cs.PLATFORM.PLATFORM_EDITOR, // 平台类型\r\n                code: this.loginInfo!.code, // 账号名\r\n                version: 1, // 版本号\r\n            }\r\n        })\r\n\r\n    }\r\n\r\n    /**\r\n     * WebSocket message event handler\r\n     */\r\n    private onWebSocketMessage(event: MessageEvent): void {\r\n        try {\r\n            logDebug(\"NetMgr\", `WebSocket message received ${event}`);\r\n            const buffer = new Uint8Array(event.data);\r\n            this.handleMessage(buffer);\r\n            this._lastHeartbeatTime = Date.now();\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to handle message: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * WebSocket close event handler\r\n     */\r\n    private onWebSocketClose(event: CloseEvent): void {\r\n        logInfo(\"NetMgr\", `WebSocket closed: ${event.code} - ${event.reason}`);\r\n        this.handleDisconnection();\r\n    }\r\n\r\n    /**\r\n     * WebSocket error event handler\r\n     */\r\n    private onWebSocketError(_event: Event): void {\r\n        logError(\"NetMgr\", \"WebSocket error occurred\");\r\n        this.handleDisconnection();\r\n    }\r\n\r\n    /**\r\n     * Handle disconnection and attempt reconnection\r\n     */\r\n    private handleDisconnection(): void {\r\n        if (this._websocket) {\r\n            this._websocket.close();\r\n            this._websocket = null;\r\n        }\r\n\r\n        this._status = NetStatus.NotConnect;\r\n\r\n        // Attempt reconnection if not manually disconnected\r\n        if (this._reconnectAttempts < this._maxReconnectAttempts) {\r\n            this._reconnectAttempts++;\r\n            logInfo(\"NetMgr\", `Attempting reconnection ${this._reconnectAttempts}/${this._maxReconnectAttempts}`);\r\n\r\n            setTimeout(() => {\r\n                if (this.loginInfo!.serverAddr && this._status !== NetStatus.Disconnected) {\r\n                    this.connect();\r\n                }\r\n            }, this._reconnectDelay);\r\n        } else {\r\n            logError(\"NetMgr\", \"Max reconnection attempts reached\");\r\n            this._status = NetStatus.Disconnected;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Process queued messages\r\n     */\r\n    private processMessageQueue(): void {\r\n        while (this._messageQueue.length > 0 && this.isConnected()) {\r\n            const message = this._messageQueue.shift();\r\n            if (message) {\r\n                this.sendRawMessage(message);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Handle incoming message\r\n     */\r\n    private handleMessage(buffer: Uint8Array): void {\r\n        try {\r\n            // Parse message header (assuming first 4 bytes are message ID)\r\n            var msg = csproto.cs.S2CMsg.decode(buffer)\r\n            if (msg.cmd != csproto.cs.CS_CMD.CS_CMD_HEARTBEAT) {\r\n                logInfo(\"NetMgr\", `Received message ${csproto.cs.CS_CMD[msg.cmd!]} ${JSON.stringify(msg)}`);\r\n            }\r\n            this.dispatchMessage(msg);\r\n\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to parse message: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dispatch message to registered handlers\r\n     */\r\n    private dispatchMessage(msg: csproto.cs.IS2CMsg): void {\r\n        if (!!msg.seq) {\r\n            this._cmdSeq.set(msg.cmd!, msg.seq);\r\n        }\r\n        const handlers = this._messageHandlers.get(msg.cmd!);\r\n        if (handlers && handlers.length > 0) {\r\n            try {\r\n                handlers.forEach(elem => {\r\n                    try {\r\n                        elem.handler.call(elem.target, msg);\r\n                    } catch (err) {\r\n                        logError(\"NetMgr\", `Handler error for msgId ${csproto.cs.CS_CMD[msg.cmd!]}: ${(err as Error).stack}`);\r\n                    }\r\n                });\r\n            } catch (err) {\r\n                logError(\"NetMgr\", `Failed to decode message ${csproto.cs.CS_CMD[msg.cmd!]}: ${(err as Error).stack}`);\r\n            }\r\n        } else {\r\n            logWarn(\"NetMgr\", `No handler registered for msgId: ${csproto.cs.CS_CMD[msg.cmd!]}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Decode protobuf message based on message ID\r\n     */\r\n    private decodeProtobufMessage(_msgId: number, data: Uint8Array): any {\r\n        // This is a simplified example - you would need to map msgId to specific protobuf types\r\n        // For now, return the raw data\r\n        return data;\r\n    }\r\n\r\n    /**\r\n     * Register message handler\r\n     */\r\n    registerHandler(msgId: csproto.cs.CS_CMD, handler: IMessageHandler, target: any): void {\r\n        if (!this._messageHandlers.has(msgId)) {\r\n            this._messageHandlers.set(msgId, []);\r\n        }\r\n        this._messageHandlers.get(msgId)!.push(new registerElem(handler, target));\r\n        logInfo(\"NetMgr\", `Registered handler for msgId: ${csproto.cs.CS_CMD[msgId]}`);\r\n    }\r\n\r\n    /**\r\n     * Unregister message handler\r\n     */\r\n    unregisterHandler(msgId: csproto.cs.CS_CMD, handler: IMessageHandler, target: any): void {\r\n        const handlers = this._messageHandlers.get(msgId);\r\n        if (handlers) {\r\n            const index = handlers.findIndex((elem) => {\r\n                return elem.eq(handler, target);\r\n            })\r\n            if (index !== -1) {\r\n                handlers.splice(index, 1);\r\n                logInfo(\"NetMgr\", `Unregistered handler for msgId: ${csproto.cs.CS_CMD[msgId]}`);\r\n\r\n                // Clean up empty handler arrays to prevent memory leaks\r\n                if (handlers.length === 0) {\r\n                    this._messageHandlers.delete(msgId);\r\n                    logInfo(\"NetMgr\", `Removed empty handler array for msgId: ${csproto.cs.CS_CMD[msgId]}`);\r\n                }\r\n            } else {\r\n                logWarn(\"NetMgr\", `Handler not found for msgId: ${csproto.cs.CS_CMD[msgId]}`);\r\n            }\r\n        } else {\r\n            logWarn(\"NetMgr\", `No handlers registered for msgId: ${csproto.cs.CS_CMD[msgId]}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Send protobuf message\r\n     */\r\n    sendMessage(msgId: csproto.cs.CS_CMD, message: csproto.cs.IC2SMsgBody): void {\r\n        if (msgId != csproto.cs.CS_CMD.CS_CMD_HEARTBEAT) {\r\n            logInfo(\"NetMgr\", `sendMessage ${csproto.cs.CS_CMD[msgId]} ${JSON.stringify(csproto.cs.C2SMsgBody.create(message))}`);\r\n        }\r\n        try {\r\n            // Encode protobuf message\r\n            const netMessage = this.encodeProtobufMessage(msgId, message);\r\n            logDebug(\"NetMgr\", `sendMessage ${csproto.cs.CS_CMD[msgId]} seq ${netMessage.seq}`);\r\n\r\n            if (this.isConnected()) {\r\n                this.sendRawMessage(netMessage);\r\n            } else {\r\n                // Queue message if not connected\r\n                this._messageQueue.push(netMessage);\r\n                logInfo(\"NetMgr\", `Queued message ${csproto.cs.CS_CMD[msgId]} seq ${netMessage.seq} (not connected)`);\r\n            }\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to send message ${csproto.cs.CS_CMD[msgId]}: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Send raw message over WebSocket\r\n     */\r\n    private sendRawMessage(message: INetMessage): void {\r\n        if (!this._websocket || this._websocket.readyState !== WebSocket.OPEN) {\r\n            logWarn(\"NetMgr\", \"WebSocket not ready for sending\");\r\n            return;\r\n        }\r\n\r\n        try {\r\n            let buffData = message.data.buffer.slice(message.data.byteOffset, message.data.byteOffset+message.data.byteLength)\r\n            this._websocket.send(buffData);\r\n            logDebug(\"NetMgr\", `Sent message ${csproto.cs.CS_CMD[message.msgId]}, size: ${message.data.byteLength}`);\r\n\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to send raw message: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encode protobuf message\r\n     */\r\n    private encodeProtobufMessage(_msgId: csproto.cs.CS_CMD, message: csproto.cs.IC2SMsgBody): INetMessage {\r\n        // This is a simplified example - you would need to map msgId to specific protobuf types\r\n        // For now, if message is already Uint8Array, return it; otherwise encode as ClientData\r\n\r\n        let seq = this._cmdSeq.get(_msgId) ?? 0;\r\n        if (message instanceof Uint8Array) {\r\n            const netMessage: INetMessage = {\r\n                msgId: _msgId,\r\n                seq: seq,\r\n                data: message\r\n            };\r\n            return netMessage;\r\n        }\r\n\r\n        var msg = new csproto.cs.C2SMsg()\r\n        msg.cmd = _msgId;\r\n        msg.seq = seq;\r\n        msg.body = message;\r\n\r\n        const clientData = csproto.cs.C2SMsg.encode(msg).finish();\r\n        const netMessage: INetMessage = {\r\n            msgId: _msgId,\r\n            seq: seq,\r\n            data: clientData,\r\n        }\r\n        return netMessage;\r\n    }\r\n\r\n    /**\r\n     * Send heartbeat message\r\n     */\r\n    private sendHeartbeat(): void {\r\n        // Send a simple heartbeat message (you can define a specific heartbeat message type)\r\n        const heartbeatData: csproto.cs.IC2SMsgBody = {\r\n            heartbeat: {\r\n                clent_time: Long.fromNumber(Date.now()), // 客户端时间\r\n                is_fighting: 0, // 是否战斗中\r\n            }\r\n        };\r\n        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_HEARTBEAT, heartbeatData);\r\n    }\r\n\r\n    /**\r\n     * Clear all message handlers\r\n     */\r\n    clearAllHandlers(): void {\r\n        this._messageHandlers.clear();\r\n        logInfo(\"NetMgr\", \"Cleared all message handlers\");\r\n    }\r\n\r\n    /**\r\n     * Get message queue length\r\n     */\r\n    getQueueLength(): number {\r\n        return this._messageQueue.length;\r\n    }\r\n\r\n    onHeartbeat(msg: csproto.cs.IS2CMsg): void {\r\n        logDebug(\"NetMgr\", `onHeartbeat ${msg}`);\r\n    }\r\n\r\n    onGetSession(msg: csproto.cs.IS2CMsg): void {\r\n        logDebug(\"NetMgr\", `onGetSession ${msg}`);\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetSession failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var sessionRsp = msg.body?.get_session;\r\n        if (!sessionRsp) {\r\n            logWarn(\"NetMgr\", \"onGetSession data is null\");\r\n            return;\r\n        }\r\n        logInfo(\"NetMgr\", `onGetSession ${sessionRsp.openid}:${sessionRsp.uin_list}`);\r\n        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, {\r\n            get_role: {\r\n                uin: Long.ZERO,\r\n                area_id: 0,\r\n            }\r\n        })\r\n    }\r\n\r\n    onGetRole(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetRole failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var roleRsp = msg.body?.get_role;\r\n        if (!roleRsp) {\r\n            logWarn(\"NetMgr\", \"onGetRole data is null\");\r\n            return;\r\n        }\r\n        DataMgr.init();\r\n        DataMgr.role.setRole(roleRsp);\r\n        if (roleRsp.cmd_seq?.items?.forEach((item) => {\r\n            this._cmdSeq.set(item.cmd!, item.seq_no!);\r\n        })) {\r\n            logInfo(\"NetMgr\", `onGetRole cmd_seq ${JSON.stringify(roleRsp.cmd_seq.items)}`);\r\n        }\r\n        DataMgr.gameLogic.checkGameID(roleRsp!.game_id!);\r\n        if (roleRsp.pvp_status != csproto.comm.GAME_PVP_STATUS.GAME_PVP_STATUS_NONE) {\r\n            this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_INFO, { game_pvp_get_info: {} });\r\n        }\r\n        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_LIST, { game_pvp_get_list: {} });\r\n    }\r\n    disableReconnect(): void {\r\n        this._reconnectAttempts = this._maxReconnectAttempts;\r\n    }\r\n}\r\n"]}