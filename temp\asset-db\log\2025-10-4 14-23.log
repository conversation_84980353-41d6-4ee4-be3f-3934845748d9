2025-10-4 14:23:57-log: Cannot access game frame or container.
2025-10-4 14:23:57-debug: asset-db:require-engine-code (411ms)
2025-10-4 14:23:57-log: meshopt wasm decoder initialized
2025-10-4 14:23:57-log: [bullet]:bullet wasm lib loaded.
2025-10-4 14:23:57-log: [box2d]:box2d wasm lib loaded.
2025-10-4 14:23:57-log: Using legacy pipeline
2025-10-4 14:23:57-log: Cocos Creator v3.8.6
2025-10-4 14:23:57-log: Forward render pipeline initialized.
2025-10-4 14:23:57-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:31.09MB, end 80.06MB, increase: 48.97MB
2025-10-4 14:23:58-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.09MB, end 224.92MB, increase: 140.83MB
2025-10-4 14:23:58-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.17MB, end 228.29MB, increase: 3.12MB
2025-10-4 14:23:57-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.19MB, end 84.05MB, increase: 2.87MB
2025-10-4 14:23:58-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.04MB, end 228.50MB, increase: 147.46MB
2025-10-4 14:23:58-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.08MB, end 228.53MB, increase: 148.44MB
2025-10-4 14:23:58-debug: run package(honor-mini-game) handler(enable) start
2025-10-4 14:23:58-debug: run package(huawei-agc) handler(enable) success!
2025-10-4 14:23:58-debug: run package(honor-mini-game) handler(enable) success!
2025-10-4 14:23:58-debug: run package(huawei-quick-game) handler(enable) start
2025-10-4 14:23:58-debug: run package(huawei-agc) handler(enable) start
2025-10-4 14:23:58-debug: run package(ios) handler(enable) start
2025-10-4 14:23:58-debug: run package(huawei-quick-game) handler(enable) success!
2025-10-4 14:23:58-debug: run package(linux) handler(enable) success!
2025-10-4 14:23:58-debug: run package(ios) handler(enable) success!
2025-10-4 14:23:58-debug: run package(mac) handler(enable) start
2025-10-4 14:23:58-debug: run package(linux) handler(enable) start
2025-10-4 14:23:58-debug: run package(mac) handler(enable) success!
2025-10-4 14:23:58-debug: run package(migu-mini-game) handler(enable) start
2025-10-4 14:23:58-debug: run package(migu-mini-game) handler(enable) success!
2025-10-4 14:23:58-debug: run package(native) handler(enable) success!
2025-10-4 14:23:58-debug: run package(native) handler(enable) start
2025-10-4 14:23:58-debug: run package(ohos) handler(enable) start
2025-10-4 14:23:58-debug: run package(ohos) handler(enable) success!
2025-10-4 14:23:58-debug: run package(oppo-mini-game) handler(enable) start
2025-10-4 14:23:58-debug: run package(runtime-dev-tools) handler(enable) start
2025-10-4 14:23:58-debug: run package(oppo-mini-game) handler(enable) success!
2025-10-4 14:23:58-debug: run package(runtime-dev-tools) handler(enable) success!
2025-10-4 14:23:58-debug: run package(vivo-mini-game) handler(enable) start
2025-10-4 14:23:58-debug: run package(taobao-mini-game) handler(enable) start
2025-10-4 14:23:58-debug: run package(taobao-mini-game) handler(enable) success!
2025-10-4 14:23:58-debug: run package(web-desktop) handler(enable) success!
2025-10-4 14:23:58-debug: run package(vivo-mini-game) handler(enable) success!
2025-10-4 14:23:58-debug: run package(web-mobile) handler(enable) start
2025-10-4 14:23:58-debug: run package(web-mobile) handler(enable) success!
2025-10-4 14:23:58-debug: run package(web-desktop) handler(enable) start
2025-10-4 14:23:58-debug: run package(wechatprogram) handler(enable) start
2025-10-4 14:23:58-debug: run package(wechatgame) handler(enable) success!
2025-10-4 14:23:58-debug: run package(wechatgame) handler(enable) start
2025-10-4 14:23:58-debug: run package(windows) handler(enable) success!
2025-10-4 14:23:58-debug: run package(xiaomi-quick-game) handler(enable) start
2025-10-4 14:23:58-debug: run package(windows) handler(enable) start
2025-10-4 14:23:58-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-10-4 14:23:58-debug: run package(cocos-service) handler(enable) start
2025-10-4 14:23:58-debug: run package(wechatprogram) handler(enable) success!
2025-10-4 14:23:58-debug: run package(cocos-service) handler(enable) success!
2025-10-4 14:23:58-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-10-4 14:23:58-debug: run package(im-plugin) handler(enable) success!
2025-10-4 14:23:58-debug: run package(im-plugin) handler(enable) start
2025-10-4 14:23:58-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-10-4 14:23:58-debug: run package(emitter-editor) handler(enable) start
2025-10-4 14:23:58-debug: run package(emitter-editor) handler(enable) success!
2025-10-4 14:23:58-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-10-4 14:23:58-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-10-4 14:23:58-debug: refresh asset db://assets/editor/enum-gen success
2025-10-4 14:23:58-debug: refresh asset db://assets/editor/enum-gen success
2025-10-4 14:23:58-debug: asset-db:worker-init: initPlugin (973ms)
2025-10-4 14:23:58-debug: run package(i18n) handler(enable) start
2025-10-4 14:23:58-debug: run package(i18n) handler(enable) success!
2025-10-4 14:23:58-debug: [Assets Memory track]: asset-db:worker-init start:31.08MB, end 225.24MB, increase: 194.16MB
2025-10-4 14:23:58-debug: Run asset db hook programming:beforePreStart ...
2025-10-4 14:23:58-debug: Run asset db hook programming:beforePreStart success!
2025-10-4 14:23:58-debug: Run asset db hook engine-extends:beforePreStart ...
2025-10-4 14:23:58-debug: Run asset db hook engine-extends:beforePreStart success!
2025-10-4 14:23:58-debug: run package(level-editor) handler(enable) start
2025-10-4 14:23:58-debug: run package(level-editor) handler(enable) success!
2025-10-4 14:23:58-debug: run package(placeholder) handler(enable) start
2025-10-4 14:23:58-debug: run package(placeholder) handler(enable) success!
2025-10-4 14:23:58-debug: asset-db:worker-init (1534ms)
2025-10-4 14:23:58-debug: asset-db-hook-programming-beforePreStart (64ms)
2025-10-4 14:23:58-debug: asset-db-hook-engine-extends-beforePreStart (63ms)
2025-10-4 14:23:58-debug: Preimport db internal success
2025-10-4 14:23:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:23:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:23:58-debug: Preimport db assets success
2025-10-4 14:23:58-debug: Run asset db hook programming:afterPreStart ...
2025-10-4 14:23:58-debug: Preimport db i18n success
2025-10-4 14:23:58-debug: starting packer-driver...
2025-10-4 14:24:05-debug: initialize scripting environment...
2025-10-4 14:24:05-debug: [[Executor]] prepare before lock
2025-10-4 14:24:05-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-10-4 14:24:05-debug: Run asset db hook programming:afterPreStart success!
2025-10-4 14:24:05-debug: [[Executor]] prepare after unlock
2025-10-4 14:24:05-debug: Run asset db hook engine-extends:afterPreStart success!
2025-10-4 14:24:05-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.25MB, end 233.35MB, increase: 8.10MB
2025-10-4 14:24:05-debug: Run asset db hook engine-extends:afterPreStart ...
2025-10-4 14:24:05-debug: Start up the 'internal' database...
2025-10-4 14:24:05-debug: asset-db-hook-programming-afterPreStart (7242ms)
2025-10-4 14:24:06-debug: asset-db-hook-engine-extends-afterPreStart (220ms)
2025-10-4 14:24:06-debug: asset-db:worker-effect-data-processing (220ms)
2025-10-4 14:24:06-debug: Start up the 'assets' database...
2025-10-4 14:24:06-debug: asset-db:worker-startup-database[internal] (7631ms)
2025-10-4 14:24:06-debug: Start up the 'i18n' database...
2025-10-4 14:24:06-debug: asset-db:worker-startup-database[assets] (7604ms)
2025-10-4 14:24:06-debug: lazy register asset handler *
2025-10-4 14:24:06-debug: lazy register asset handler text
2025-10-4 14:24:06-debug: lazy register asset handler json
2025-10-4 14:24:06-debug: lazy register asset handler spine-data
2025-10-4 14:24:06-debug: lazy register asset handler dragonbones-atlas
2025-10-4 14:24:06-debug: lazy register asset handler dragonbones
2025-10-4 14:24:06-debug: lazy register asset handler javascript
2025-10-4 14:24:06-debug: lazy register asset handler terrain
2025-10-4 14:24:06-debug: lazy register asset handler typescript
2025-10-4 14:24:06-debug: lazy register asset handler directory
2025-10-4 14:24:06-debug: lazy register asset handler prefab
2025-10-4 14:24:06-debug: lazy register asset handler sprite-frame
2025-10-4 14:24:06-debug: lazy register asset handler buffer
2025-10-4 14:24:06-debug: lazy register asset handler tiled-map
2025-10-4 14:24:06-debug: lazy register asset handler image
2025-10-4 14:24:06-debug: lazy register asset handler scene
2025-10-4 14:24:06-debug: lazy register asset handler sign-image
2025-10-4 14:24:06-debug: lazy register asset handler alpha-image
2025-10-4 14:24:06-debug: lazy register asset handler texture-cube
2025-10-4 14:24:06-debug: lazy register asset handler texture
2025-10-4 14:24:06-debug: lazy register asset handler render-texture
2025-10-4 14:24:06-debug: lazy register asset handler erp-texture-cube
2025-10-4 14:24:06-debug: lazy register asset handler texture-cube-face
2025-10-4 14:24:06-debug: lazy register asset handler gltf
2025-10-4 14:24:06-debug: lazy register asset handler rt-sprite-frame
2025-10-4 14:24:06-debug: lazy register asset handler gltf-animation
2025-10-4 14:24:06-debug: lazy register asset handler gltf-material
2025-10-4 14:24:06-debug: lazy register asset handler gltf-mesh
2025-10-4 14:24:06-debug: lazy register asset handler gltf-scene
2025-10-4 14:24:06-debug: lazy register asset handler gltf-embeded-image
2025-10-4 14:24:06-debug: lazy register asset handler gltf-skeleton
2025-10-4 14:24:06-debug: lazy register asset handler fbx
2025-10-4 14:24:06-debug: lazy register asset handler physics-material
2025-10-4 14:24:06-debug: lazy register asset handler material
2025-10-4 14:24:06-debug: lazy register asset handler audio-clip
2025-10-4 14:24:06-debug: lazy register asset handler effect
2025-10-4 14:24:06-debug: lazy register asset handler animation-graph
2025-10-4 14:24:06-debug: lazy register asset handler effect-header
2025-10-4 14:24:06-debug: lazy register asset handler animation-graph-variant
2025-10-4 14:24:06-debug: lazy register asset handler ttf-font
2025-10-4 14:24:06-debug: lazy register asset handler animation-clip
2025-10-4 14:24:06-debug: lazy register asset handler bitmap-font
2025-10-4 14:24:06-debug: lazy register asset handler animation-mask
2025-10-4 14:24:06-debug: lazy register asset handler particle
2025-10-4 14:24:06-debug: lazy register asset handler sprite-atlas
2025-10-4 14:24:06-debug: lazy register asset handler auto-atlas
2025-10-4 14:24:06-debug: lazy register asset handler render-stage
2025-10-4 14:24:06-debug: lazy register asset handler render-pipeline
2025-10-4 14:24:06-debug: lazy register asset handler instantiation-material
2025-10-4 14:24:06-debug: lazy register asset handler label-atlas
2025-10-4 14:24:06-debug: lazy register asset handler instantiation-mesh
2025-10-4 14:24:06-debug: lazy register asset handler render-flow
2025-10-4 14:24:06-debug: lazy register asset handler instantiation-skeleton
2025-10-4 14:24:06-debug: lazy register asset handler instantiation-animation
2025-10-4 14:24:06-debug: lazy register asset handler video-clip
2025-10-4 14:24:06-debug: asset-db:worker-startup-database[i18n] (7546ms)
2025-10-4 14:24:06-debug: asset-db:start-database (7734ms)
2025-10-4 14:24:06-debug: asset-db:ready (10815ms)
2025-10-4 14:24:06-debug: init worker message success
2025-10-4 14:24:06-debug: fix the bug of updateDefaultUserData
2025-10-4 14:24:06-debug: programming:execute-script (3ms)
2025-10-4 14:24:06-debug: [Build Memory track]: builder:worker-init start:193.17MB, end 206.49MB, increase: 13.32MB
2025-10-4 14:24:06-debug: builder:worker-init (296ms)
2025-10-4 14:24:11-debug: refresh db internal success
2025-10-4 14:24:11-debug: refresh db assets success
2025-10-4 14:24:11-debug: refresh db i18n success
2025-10-4 14:24:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:24:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:24:11-debug: asset-db:refresh-all-database (186ms)
2025-10-4 14:24:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-4 14:24:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:24:18-debug: refresh db internal success
2025-10-4 14:24:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresenemy.json
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:24:18-debug: refresh db assets success
2025-10-4 14:24:18-debug: refresh db i18n success
2025-10-4 14:24:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:24:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:24:18-debug: asset-db:refresh-all-database (158ms)
2025-10-4 14:24:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-4 14:24:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:25:23-debug: refresh db internal success
2025-10-4 14:25:23-debug: refresh db assets success
2025-10-4 14:25:23-debug: refresh db i18n success
2025-10-4 14:25:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:25:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:25:23-debug: asset-db:refresh-all-database (199ms)
2025-10-4 14:25:28-debug: refresh db internal success
2025-10-4 14:25:28-debug: refresh db assets success
2025-10-4 14:25:28-debug: refresh db i18n success
2025-10-4 14:25:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:25:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:25:28-debug: asset-db:refresh-all-database (157ms)
2025-10-4 14:25:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-4 14:25:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:25:33-debug: refresh db internal success
2025-10-4 14:25:33-debug: refresh db assets success
2025-10-4 14:25:33-debug: refresh db i18n success
2025-10-4 14:25:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:25:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:25:33-debug: asset-db:refresh-all-database (158ms)
2025-10-4 14:25:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-4 14:25:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:25:43-debug: refresh db internal success
2025-10-4 14:25:43-debug: refresh db assets success
2025-10-4 14:25:43-debug: refresh db i18n success
2025-10-4 14:25:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:25:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:25:43-debug: asset-db:refresh-all-database (150ms)
2025-10-4 14:27:36-debug: refresh db internal success
2025-10-4 14:27:36-debug: refresh db assets success
2025-10-4 14:27:36-debug: refresh db i18n success
2025-10-4 14:27:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:27:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:27:36-debug: asset-db:refresh-all-database (194ms)
2025-10-4 14:27:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:40:39-debug: refresh db internal success
2025-10-4 14:40:39-debug: refresh db assets success
2025-10-4 14:40:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:40:39-debug: refresh db i18n success
2025-10-4 14:40:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:40:39-debug: asset-db:refresh-all-database (204ms)
2025-10-4 14:40:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:46:39-debug: refresh db internal success
2025-10-4 14:46:39-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:46:39-debug: refresh db assets success
2025-10-4 14:46:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:46:39-debug: refresh db i18n success
2025-10-4 14:46:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:46:39-debug: asset-db:refresh-all-database (212ms)
2025-10-4 14:48:33-debug: refresh db internal success
2025-10-4 14:48:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMoveDebugHelper.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:48:33-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:48:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:48:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:48:33-debug: refresh db assets success
2025-10-4 14:48:33-debug: refresh db i18n success
2025-10-4 14:48:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:48:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:48:33-debug: asset-db:refresh-all-database (220ms)
2025-10-4 14:48:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:48:36-debug: refresh db internal success
2025-10-4 14:48:36-debug: refresh db assets success
2025-10-4 14:48:36-debug: refresh db i18n success
2025-10-4 14:48:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:48:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:48:36-debug: asset-db:refresh-all-database (198ms)
2025-10-4 14:48:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:48:40-debug: refresh db internal success
2025-10-4 14:48:40-debug: refresh db assets success
2025-10-4 14:48:40-debug: refresh db i18n success
2025-10-4 14:48:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:48:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:48:40-debug: asset-db:refresh-all-database (165ms)
2025-10-4 14:50:50-debug: refresh db internal success
2025-10-4 14:50:50-debug: refresh db assets success
2025-10-4 14:50:50-debug: refresh db i18n success
2025-10-4 14:50:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:50:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:50:50-debug: asset-db:refresh-all-database (196ms)
2025-10-4 14:56:42-debug: refresh db internal success
2025-10-4 14:56:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:56:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMoveDebugHelper.ts
background: #aaff85; color: #000;
color: #000;
2025-10-4 14:56:42-debug: refresh db assets success
2025-10-4 14:56:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:56:42-debug: refresh db i18n success
2025-10-4 14:56:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:56:42-debug: asset-db:refresh-all-database (195ms)
2025-10-4 14:56:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-4 14:56:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:56:45-debug: refresh db internal success
2025-10-4 14:56:45-debug: refresh db assets success
2025-10-4 14:56:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:56:45-debug: refresh db i18n success
2025-10-4 14:56:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:56:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-4 14:56:45-debug: asset-db:refresh-all-database (180ms)
2025-10-4 14:56:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-4 14:56:53-debug: refresh db internal success
2025-10-4 14:56:53-debug: refresh db assets success
2025-10-4 14:56:53-debug: refresh db i18n success
2025-10-4 14:56:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-4 14:56:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-4 14:56:53-debug: asset-db:refresh-all-database (150ms)
2025-10-4 14:56:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-4 14:56:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
