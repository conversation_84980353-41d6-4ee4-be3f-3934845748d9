{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathSamplingDebugHelper.ts"], "names": ["_decorator", "Component", "CCInteger", "CCFloat", "Enum", "<PERSON><PERSON>", "Label", "PathData", "eSamplingStrategy", "ccclass", "property", "PathSamplingDebugHelper", "type", "displayName", "tooltip", "range", "slide", "onLoad", "testButton", "node", "on", "EventType", "CLICK", "testCurrentStrategy", "compareButton", "compareAllStrategies", "testPathData", "logResult", "originalStrategy", "samplingStrategy", "currentStrategy", "startTime", "performance", "now", "sampledPoints", "getSubdividedPoints", "endTime", "analysis", "analyzePathSampling", "processingTime", "strategyName", "getStrategyName", "result", "toFixed", "totalPoints", "avgDistance", "distanceStdDev", "maxDistance", "minDistance", "speedVariation", "strategies", "UniformDistance", "AdaptiveSubdivision", "results", "strategy", "push", "compareResult", "join", "points", "length", "distances", "speeds", "i", "dist", "Math", "sqrt", "pow", "x", "y", "speed", "reduce", "a", "b", "distanceVariance", "sum", "max", "min", "abs", "message", "console", "log", "resultLabel", "string", "setTestParameters", "targetDistance", "smoothingFactor", "targetSamplingDistance", "speedSmoothingFactor", "getRecommendedSettings", "distance", "avgSpeed", "p", "recommendedDistance"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AAC/DC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OACb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;AAE9B;AACA;AACA;AACA;;yCAEaW,uB,WADZF,OAAO,CAAC,yBAAD,C,UAGHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI;AAAA;AAAA,gCAAN;AAAkBC,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAER,IAAI;AAAA;AAAA,mDAAZ;AAAiCS,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEV,SAAR;AAAmBW,QAAAA,WAAW,EAAE,QAAhC;AAA0CC,QAAAA,OAAO,EAAE;AAAnD,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE,QAA9B;AAAwCE,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA/C;AAAuDC,QAAAA,KAAK,EAAE;AAA9D,OAAD,C,UAGRN,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,MAAR;AAAgBQ,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEP,MAAR;AAAgBQ,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN,KAAR;AAAeO,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,2BArBb,MACaF,uBADb,SAC6CV,SAD7C,CACuD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAuBnDgB,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,IAAhB,CAAqBC,EAArB,CAAwBf,MAAM,CAACgB,SAAP,CAAiBC,KAAzC,EAAgD,KAAKC,mBAArD,EAA0E,IAA1E;AACH;;AACD,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBL,IAAnB,CAAwBC,EAAxB,CAA2Bf,MAAM,CAACgB,SAAP,CAAiBC,KAA5C,EAAmD,KAAKG,oBAAxD,EAA8E,IAA9E;AACH;AACJ;AAED;AACJ;AACA;;;AACWF,QAAAA,mBAAmB,GAAG;AACzB,cAAI,CAAC,KAAKG,YAAV,EAAwB;AACpB,iBAAKC,SAAL,CAAe,cAAf;AACA;AACH,WAJwB,CAMzB;;;AACA,gBAAMC,gBAAgB,GAAG,KAAKF,YAAL,CAAkBG,gBAA3C,CAPyB,CASzB;;AACA,eAAKH,YAAL,CAAkBG,gBAAlB,GAAqC,KAAKC,eAA1C,CAVyB,CAYzB;;AACA,gBAAMC,SAAS,GAAGC,WAAW,CAACC,GAAZ,EAAlB;AACA,gBAAMC,aAAa,GAAG,KAAKR,YAAL,CAAkBS,mBAAlB,CAAsC,IAAtC,CAAtB;AACA,gBAAMC,OAAO,GAAGJ,WAAW,CAACC,GAAZ,EAAhB,CAfyB,CAiBzB;;AACA,gBAAMI,QAAQ,GAAG,KAAKC,mBAAL,CAAyBJ,aAAzB,CAAjB;AACA,gBAAMK,cAAc,GAAGH,OAAO,GAAGL,SAAjC,CAnByB,CAqBzB;;AACA,eAAKL,YAAL,CAAkBG,gBAAlB,GAAqCD,gBAArC,CAtByB,CAwBzB;;AACA,gBAAMY,YAAY,GAAG,KAAKC,eAAL,CAAqB,KAAKX,eAA1B,CAArB;AACA,gBAAMY,MAAM,GAAI;AACxB,MAAMF,YAAa;AACnB,QAAQD,cAAc,CAACI,OAAf,CAAuB,CAAvB,CAA0B;AAClC,QAAQN,QAAQ,CAACO,WAAY;AAC7B,QAAQP,QAAQ,CAACQ,WAAT,CAAqBF,OAArB,CAA6B,CAA7B,CAAgC;AACxC,SAASN,QAAQ,CAACS,cAAT,CAAwBH,OAAxB,CAAgC,CAAhC,CAAmC;AAC5C,QAAQN,QAAQ,CAACU,WAAT,CAAqBJ,OAArB,CAA6B,CAA7B,CAAgC;AACxC,QAAQN,QAAQ,CAACW,WAAT,CAAqBL,OAArB,CAA6B,CAA7B,CAAgC;AACxC,QAAQN,QAAQ,CAACY,cAAT,CAAwBN,OAAxB,CAAgC,CAAhC,CAAmC;AAC3C,SATQ;AAWA,eAAKhB,SAAL,CAAee,MAAf;AACH;AAED;AACJ;AACA;;;AACWjB,QAAAA,oBAAoB,GAAG;AAC1B,cAAI,CAAC,KAAKC,YAAV,EAAwB;AACpB,iBAAKC,SAAL,CAAe,cAAf;AACA;AACH;;AAED,gBAAMuB,UAAU,GAAG,CAAC;AAAA;AAAA,sDAAkBC,eAAnB,EAAoC;AAAA;AAAA,sDAAkBC,mBAAtD,CAAnB;AACA,gBAAMC,OAAiB,GAAG,EAA1B,CAP0B,CAS1B;;AACA,gBAAMzB,gBAAgB,GAAG,KAAKF,YAAL,CAAkBG,gBAA3C;;AAEA,eAAK,MAAMyB,QAAX,IAAuBJ,UAAvB,EAAmC;AAC/B,iBAAKxB,YAAL,CAAkBG,gBAAlB,GAAqCyB,QAArC;AAEA,kBAAMvB,SAAS,GAAGC,WAAW,CAACC,GAAZ,EAAlB;AACA,kBAAMC,aAAa,GAAG,KAAKR,YAAL,CAAkBS,mBAAlB,CAAsC,IAAtC,CAAtB;AACA,kBAAMC,OAAO,GAAGJ,WAAW,CAACC,GAAZ,EAAhB;AAEA,kBAAMI,QAAQ,GAAG,KAAKC,mBAAL,CAAyBJ,aAAzB,CAAjB;AACA,kBAAMK,cAAc,GAAGH,OAAO,GAAGL,SAAjC;AAEA,kBAAMS,YAAY,GAAG,KAAKC,eAAL,CAAqBa,QAArB,CAArB;AACAD,YAAAA,OAAO,CAACE,IAAR,CAAc;AAC1B,EAAEf,YAAa;AACf,QAAQD,cAAc,CAACI,OAAf,CAAuB,CAAvB,CAA0B;AAClC,QAAQN,QAAQ,CAACO,WAAY;AAC7B,UAAUP,QAAQ,CAACQ,WAAT,CAAqBF,OAArB,CAA6B,CAA7B,CAAgC;AAC1C,WAAWN,QAAQ,CAACS,cAAT,CAAwBH,OAAxB,CAAgC,CAAhC,CAAmC;AAC9C,UAAUN,QAAQ,CAACY,cAAT,CAAwBN,OAAxB,CAAgC,CAAhC,CAAmC;AAC7C,aAPY;AAQH,WA/ByB,CAiC1B;;;AACA,eAAKjB,YAAL,CAAkBG,gBAAlB,GAAqCD,gBAArC,CAlC0B,CAoC1B;;AACA,gBAAM4B,aAAa,GAAG,mBAAmBH,OAAO,CAACI,IAAR,CAAa,IAAb,CAAzC;AACA,eAAK9B,SAAL,CAAe6B,aAAf;AACH;AAED;AACJ;AACA;;;AACYlB,QAAAA,mBAAmB,CAACoB,MAAD,EAAqB;AAC5C,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AACnB,mBAAO;AACHf,cAAAA,WAAW,EAAEc,MAAM,CAACC,MADjB;AAEHd,cAAAA,WAAW,EAAE,CAFV;AAGHC,cAAAA,cAAc,EAAE,CAHb;AAIHC,cAAAA,WAAW,EAAE,CAJV;AAKHC,cAAAA,WAAW,EAAE,CALV;AAMHC,cAAAA,cAAc,EAAE;AANb,aAAP;AAQH,WAV2C,CAY5C;;;AACA,gBAAMW,SAAmB,GAAG,EAA5B;AACA,gBAAMC,MAAgB,GAAG,EAAzB;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAAM,CAACC,MAA3B,EAAmCG,CAAC,EAApC,EAAwC;AACpC,kBAAMC,IAAI,GAAGC,IAAI,CAACC,IAAL,CACTD,IAAI,CAACE,GAAL,CAASR,MAAM,CAACI,CAAD,CAAN,CAAUK,CAAV,GAAcT,MAAM,CAACI,CAAC,GAAC,CAAH,CAAN,CAAYK,CAAnC,EAAsC,CAAtC,IACAH,IAAI,CAACE,GAAL,CAASR,MAAM,CAACI,CAAD,CAAN,CAAUM,CAAV,GAAcV,MAAM,CAACI,CAAC,GAAC,CAAH,CAAN,CAAYM,CAAnC,EAAsC,CAAtC,CAFS,CAAb;AAIAR,YAAAA,SAAS,CAACL,IAAV,CAAeQ,IAAf;AACAF,YAAAA,MAAM,CAACN,IAAP,CAAYG,MAAM,CAACI,CAAD,CAAN,CAAUO,KAAtB;AACH,WAvB2C,CAyB5C;;;AACA,gBAAMxB,WAAW,GAAGe,SAAS,CAACU,MAAV,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAGC,CAA/B,EAAkC,CAAlC,IAAuCZ,SAAS,CAACD,MAArE;AACA,gBAAMc,gBAAgB,GAAGb,SAAS,CAACU,MAAV,CAAiB,CAACI,GAAD,EAAMX,IAAN,KAAeW,GAAG,GAAGV,IAAI,CAACE,GAAL,CAASH,IAAI,GAAGlB,WAAhB,EAA6B,CAA7B,CAAtC,EAAuE,CAAvE,IAA4Ee,SAAS,CAACD,MAA/G;AACA,gBAAMb,cAAc,GAAGkB,IAAI,CAACC,IAAL,CAAUQ,gBAAV,CAAvB;AACA,gBAAM1B,WAAW,GAAGiB,IAAI,CAACW,GAAL,CAAS,GAAGf,SAAZ,CAApB;AACA,gBAAMZ,WAAW,GAAGgB,IAAI,CAACY,GAAL,CAAS,GAAGhB,SAAZ,CAApB,CA9B4C,CAgC5C;;AACA,cAAIX,cAAc,GAAG,CAArB;;AACA,cAAIY,MAAM,CAACF,MAAP,GAAgB,CAApB,EAAuB;AACnB,iBAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,MAAM,CAACF,MAA3B,EAAmCG,CAAC,EAApC,EAAwC;AACpCb,cAAAA,cAAc,IAAIe,IAAI,CAACa,GAAL,CAAShB,MAAM,CAACC,CAAD,CAAN,GAAYD,MAAM,CAACC,CAAC,GAAC,CAAH,CAA3B,CAAlB;AACH;;AACDb,YAAAA,cAAc,IAAIY,MAAM,CAACF,MAAP,GAAgB,CAAlC;AACH;;AAED,iBAAO;AACHf,YAAAA,WAAW,EAAEc,MAAM,CAACC,MADjB;AAEHd,YAAAA,WAFG;AAGHC,YAAAA,cAHG;AAIHC,YAAAA,WAJG;AAKHC,YAAAA,WALG;AAMHC,YAAAA;AANG,WAAP;AAQH;AAED;AACJ;AACA;;;AACYR,QAAAA,eAAe,CAACa,QAAD,EAAsC;AACzD,kBAAQA,QAAR;AACI,iBAAK;AAAA;AAAA,wDAAkBH,eAAvB;AACI,qBAAO,QAAP;;AACJ,iBAAK;AAAA;AAAA,wDAAkBC,mBAAvB;AACI,qBAAO,OAAP;;AACJ;AACI,qBAAO,MAAP;AANR;AAQH;AAED;AACJ;AACA;;;AACYzB,QAAAA,SAAS,CAACmD,OAAD,EAAkB;AAC/BC,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCF,OAAnC;;AACA,cAAI,KAAKG,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBC,MAAjB,GAA0BJ,OAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACWK,QAAAA,iBAAiB,CAACC,cAAD,EAAyBC,eAAzB,EAAkD;AACtE,eAAKC,sBAAL,GAA8BF,cAA9B;AACA,eAAKG,oBAAL,GAA4BF,eAA5B;AACH;AAED;AACJ;AACA;;;AACWG,QAAAA,sBAAsB,GAAsD;AAC/E;AACA,cAAI,CAAC,KAAK9D,YAAV,EAAwB;AACpB,mBAAO;AAAE4B,cAAAA,QAAQ,EAAE;AAAA;AAAA,0DAAkBH,eAA9B;AAA+CsC,cAAAA,QAAQ,EAAE;AAAzD,aAAP;AACH;;AAED,gBAAM/B,MAAM,GAAG,KAAKhC,YAAL,CAAkBgC,MAAjC;;AACA,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AACnB,mBAAO;AAAEL,cAAAA,QAAQ,EAAE;AAAA;AAAA,0DAAkBH,eAA9B;AAA+CsC,cAAAA,QAAQ,EAAE;AAAzD,aAAP;AACH,WAT8E,CAW/E;;;AACA,gBAAMC,QAAQ,GAAGhC,MAAM,CAACY,MAAP,CAAc,CAACI,GAAD,EAAMiB,CAAN,KAAYjB,GAAG,GAAGiB,CAAC,CAACtB,KAAlC,EAAyC,CAAzC,IAA8CX,MAAM,CAACC,MAAtE,CAZ+E,CAc/E;;AACA,gBAAMiC,mBAAmB,GAAG5B,IAAI,CAACW,GAAL,CAAS,CAAT,EAAYX,IAAI,CAACY,GAAL,CAAS,EAAT,EAAac,QAAQ,GAAG,EAAxB,CAAZ,CAA5B;AAEA,iBAAO;AACHpC,YAAAA,QAAQ,EAAE;AAAA;AAAA,wDAAkBH,eADzB;AAEHsC,YAAAA,QAAQ,EAAEG;AAFP,WAAP;AAIH;;AArOkD,O;;;;;iBAGZ,I;;;;;;;iBAGK;AAAA;AAAA,sDAAkBzC,e;;;;;;;iBAGtB,E;;;;;;;iBAGF,G;;;;;;;iBAGH,I;;;;;;;iBAGG,I;;;;;;;iBAGH,I", "sourcesContent": ["import { _decorator, Component, Node, CCInteger, CCFloat, Enum, Button, Label } from 'cc';\nimport { PathData, eSamplingStrategy } from './PathData';\nconst { ccclass, property } = _decorator;\n\n/**\n * 路径采样调试助手\n * 用于测试和比较不同的采样策略效果\n */\n@ccclass('PathSamplingDebugHelper')\nexport class PathSamplingDebugHelper extends Component {\n\n    @property({ type: PathData, displayName: \"测试路径数据\" })\n    public testPathData: PathData | null = null;\n\n    @property({ type: Enum(eSamplingStrategy), displayName: \"当前采样策略\" })\n    public currentStrategy: eSamplingStrategy = eSamplingStrategy.UniformDistance;\n\n    @property({ type: CCInteger, displayName: \"目标采样距离\", tooltip: \"仅对均匀距离采样有效\" })\n    public targetSamplingDistance: number = 20;\n\n    @property({ type: CCFloat, displayName: \"速度平滑系数\", range: [0, 1], slide: true })\n    public speedSmoothingFactor: number = 0.3;\n\n    @property({ type: Button, displayName: \"测试当前策略\" })\n    public testButton: Button | null = null;\n\n    @property({ type: Button, displayName: \"比较所有策略\" })\n    public compareButton: Button | null = null;\n\n    @property({ type: Label, displayName: \"结果显示\" })\n    public resultLabel: Label | null = null;\n\n    onLoad() {\n        if (this.testButton) {\n            this.testButton.node.on(Button.EventType.CLICK, this.testCurrentStrategy, this);\n        }\n        if (this.compareButton) {\n            this.compareButton.node.on(Button.EventType.CLICK, this.compareAllStrategies, this);\n        }\n    }\n\n    /**\n     * 测试当前采样策略\n     */\n    public testCurrentStrategy() {\n        if (!this.testPathData) {\n            this.logResult(\"错误：未设置测试路径数据\");\n            return;\n        }\n\n        // 备份原始设置\n        const originalStrategy = this.testPathData.samplingStrategy;\n\n        // 应用测试设置\n        this.testPathData.samplingStrategy = this.currentStrategy;\n\n        // 生成采样点\n        const startTime = performance.now();\n        const sampledPoints = this.testPathData.getSubdividedPoints(true);\n        const endTime = performance.now();\n\n        // 分析结果\n        const analysis = this.analyzePathSampling(sampledPoints);\n        const processingTime = endTime - startTime;\n\n        // 恢复原始设置\n        this.testPathData.samplingStrategy = originalStrategy;\n\n        // 显示结果\n        const strategyName = this.getStrategyName(this.currentStrategy);\n        const result = `\n策略: ${strategyName}\n处理时间: ${processingTime.toFixed(2)}ms\n采样点数: ${analysis.totalPoints}\n平均距离: ${analysis.avgDistance.toFixed(2)}px\n距离标准差: ${analysis.distanceStdDev.toFixed(2)}px\n最大距离: ${analysis.maxDistance.toFixed(2)}px\n最小距离: ${analysis.minDistance.toFixed(2)}px\n速度变化: ${analysis.speedVariation.toFixed(2)}\n        `;\n\n        this.logResult(result);\n    }\n\n    /**\n     * 比较所有采样策略\n     */\n    public compareAllStrategies() {\n        if (!this.testPathData) {\n            this.logResult(\"错误：未设置测试路径数据\");\n            return;\n        }\n\n        const strategies = [eSamplingStrategy.UniformDistance, eSamplingStrategy.AdaptiveSubdivision];\n        const results: string[] = [];\n\n        // 备份原始设置\n        const originalStrategy = this.testPathData.samplingStrategy;\n\n        for (const strategy of strategies) {\n            this.testPathData.samplingStrategy = strategy;\n\n            const startTime = performance.now();\n            const sampledPoints = this.testPathData.getSubdividedPoints(true);\n            const endTime = performance.now();\n\n            const analysis = this.analyzePathSampling(sampledPoints);\n            const processingTime = endTime - startTime;\n\n            const strategyName = this.getStrategyName(strategy);\n            results.push(`\n${strategyName}:\n  时间: ${processingTime.toFixed(2)}ms\n  点数: ${analysis.totalPoints}\n  平均距离: ${analysis.avgDistance.toFixed(2)}px\n  距离标准差: ${analysis.distanceStdDev.toFixed(2)}px\n  速度变化: ${analysis.speedVariation.toFixed(2)}\n            `);\n        }\n\n        // 恢复原始设置\n        this.testPathData.samplingStrategy = originalStrategy;\n\n        // 显示比较结果\n        const compareResult = \"=== 采样策略比较 ===\" + results.join(\"\\n\");\n        this.logResult(compareResult);\n    }\n\n    /**\n     * 分析路径采样质量\n     */\n    private analyzePathSampling(points: any[]): any {\n        if (points.length < 2) {\n            return {\n                totalPoints: points.length,\n                avgDistance: 0,\n                distanceStdDev: 0,\n                maxDistance: 0,\n                minDistance: 0,\n                speedVariation: 0\n            };\n        }\n\n        // 计算相邻点距离\n        const distances: number[] = [];\n        const speeds: number[] = [];\n\n        for (let i = 1; i < points.length; i++) {\n            const dist = Math.sqrt(\n                Math.pow(points[i].x - points[i-1].x, 2) + \n                Math.pow(points[i].y - points[i-1].y, 2)\n            );\n            distances.push(dist);\n            speeds.push(points[i].speed);\n        }\n\n        // 统计距离\n        const avgDistance = distances.reduce((a, b) => a + b, 0) / distances.length;\n        const distanceVariance = distances.reduce((sum, dist) => sum + Math.pow(dist - avgDistance, 2), 0) / distances.length;\n        const distanceStdDev = Math.sqrt(distanceVariance);\n        const maxDistance = Math.max(...distances);\n        const minDistance = Math.min(...distances);\n\n        // 计算速度变化\n        let speedVariation = 0;\n        if (speeds.length > 1) {\n            for (let i = 1; i < speeds.length; i++) {\n                speedVariation += Math.abs(speeds[i] - speeds[i-1]);\n            }\n            speedVariation /= speeds.length - 1;\n        }\n\n        return {\n            totalPoints: points.length,\n            avgDistance,\n            distanceStdDev,\n            maxDistance,\n            minDistance,\n            speedVariation\n        };\n    }\n\n    /**\n     * 获取策略名称\n     */\n    private getStrategyName(strategy: eSamplingStrategy): string {\n        switch (strategy) {\n            case eSamplingStrategy.UniformDistance:\n                return \"均匀距离采样\";\n            case eSamplingStrategy.AdaptiveSubdivision:\n                return \"自适应细分\";\n            default:\n                return \"未知策略\";\n        }\n    }\n\n    /**\n     * 显示结果\n     */\n    private logResult(message: string) {\n        console.log(\"[PathSamplingDebug]\", message);\n        if (this.resultLabel) {\n            this.resultLabel.string = message;\n        }\n    }\n\n    /**\n     * 设置测试参数\n     */\n    public setTestParameters(targetDistance: number, smoothingFactor: number) {\n        this.targetSamplingDistance = targetDistance;\n        this.speedSmoothingFactor = smoothingFactor;\n    }\n\n    /**\n     * 获取推荐设置\n     */\n    public getRecommendedSettings(): { strategy: eSamplingStrategy, distance: number } {\n        // 基于路径复杂度推荐设置\n        if (!this.testPathData) {\n            return { strategy: eSamplingStrategy.UniformDistance, distance: 20 };\n        }\n\n        const points = this.testPathData.points;\n        if (points.length < 3) {\n            return { strategy: eSamplingStrategy.UniformDistance, distance: 10 };\n        }\n\n        // 计算路径的平均速度\n        const avgSpeed = points.reduce((sum, p) => sum + p.speed, 0) / points.length;\n        \n        // 基于速度推荐采样距离\n        const recommendedDistance = Math.max(5, Math.min(50, avgSpeed / 30));\n\n        return {\n            strategy: eSamplingStrategy.UniformDistance,\n            distance: recommendedDistance\n        };\n    }\n}\n"]}