System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, EnemyEnum;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d06401MB8xHWoWcxFreSfDM", "EnemyEnum", undefined);

      _export("EnemyEnum", EnemyEnum = /*#__PURE__*/function (EnemyEnum) {
        EnemyEnum[EnemyEnum["\u5C0F\u654C\u673A1"] = 1] = "\u5C0F\u654C\u673A1";
        EnemyEnum[EnemyEnum["\u4E2D\u654C\u673A"] = 2] = "\u4E2D\u654C\u673A";
        EnemyEnum[EnemyEnum["\u5927\u654C\u673A"] = 3] = "\u5927\u654C\u673A";
        EnemyEnum[EnemyEnum["\u5C0FBOSS"] = 100001] = "\u5C0FBOSS";
        EnemyEnum[EnemyEnum["\u4E2DBOSS"] = 100002] = "\u4E2DBOSS";
        EnemyEnum[EnemyEnum["\u5927BOSS"] = 100003] = "\u5927BOSS";
        EnemyEnum[EnemyEnum["\u5C0F\u654C\u673A1\u84DD1"] = 4] = "\u5C0F\u654C\u673A1\u84DD1";
        EnemyEnum[EnemyEnum["\u5C0F\u654C\u673A1\u80D6"] = 5] = "\u5C0F\u654C\u673A1\u80D6";
        EnemyEnum[EnemyEnum["\u5C0F\u654C\u673A1\u84DD2"] = 6] = "\u5C0F\u654C\u673A1\u84DD2";
        EnemyEnum[EnemyEnum["\u5C0F\u654C\u673A1\u7EFF"] = 7] = "\u5C0F\u654C\u673A1\u7EFF";
        return EnemyEnum;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2658d2b36a86a0f612b98d6717158b5bf8447779.js.map