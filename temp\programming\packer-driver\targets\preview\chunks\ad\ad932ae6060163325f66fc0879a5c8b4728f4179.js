System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, PathMove, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, PathMoveDebugHelper;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPathMove(extras) {
    _reporterNs.report("PathMove", "./PathMove", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      PathMove = _unresolved_2.PathMove;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6d838tBLaBMWaQQbCaRTb/L", "PathMoveDebugHelper", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * PathMove 调试助手
       * 用于快速启用/禁用 PathMove 的调试日志，并配置调试阈值
       */

      _export("PathMoveDebugHelper", PathMoveDebugHelper = (_dec = ccclass('PathMoveDebugHelper'), _dec2 = property({
        displayName: "启用调试日志"
      }), _dec3 = property({
        displayName: "位置跳跃阈值(像素)",
        tooltip: "超过此值会警告位置跳跃"
      }), _dec4 = property({
        displayName: "单帧移动阈值(像素)",
        tooltip: "超过此值会警告单帧移动过大"
      }), _dec5 = property({
        displayName: "加速度阈值",
        tooltip: "超过此值会警告加速度过大"
      }), _dec6 = property({
        displayName: "帧时间阈值(毫秒)",
        tooltip: "超过此值会警告帧时间过长"
      }), _dec(_class = (_class2 = class PathMoveDebugHelper extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "enableDebug", _descriptor, this);

          _initializerDefineProperty(this, "positionJumpThreshold", _descriptor2, this);

          _initializerDefineProperty(this, "singleFrameMoveThreshold", _descriptor3, this);

          _initializerDefineProperty(this, "accelerationThreshold", _descriptor4, this);

          _initializerDefineProperty(this, "frameTimeThreshold", _descriptor5, this);
        }

        onLoad() {
          // 设置调试状态
          (_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
            error: Error()
          }), PathMove) : PathMove).setDebugEnabled(this.enableDebug); // 设置调试阈值

          this.updateThresholds();
        }
        /**
         * 更新调试阈值
         */


        updateThresholds() {
          (_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
            error: Error()
          }), PathMove) : PathMove).setDebugThresholds({
            positionJump: this.positionJumpThreshold,
            singleFrameMove: this.singleFrameMoveThreshold,
            acceleration: this.accelerationThreshold,
            frameTime: this.frameTimeThreshold / 1000 // 转换为秒

          });
        }
        /**
         * 在运行时切换调试状态
         */


        toggleDebug() {
          this.enableDebug = !this.enableDebug;
          (_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
            error: Error()
          }), PathMove) : PathMove).setDebugEnabled(this.enableDebug);
        }
        /**
         * 启用调试日志
         */


        enableDebugLog() {
          this.enableDebug = true;
          (_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
            error: Error()
          }), PathMove) : PathMove).setDebugEnabled(true);
        }
        /**
         * 禁用调试日志
         */


        disableDebugLog() {
          this.enableDebug = false;
          (_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
            error: Error()
          }), PathMove) : PathMove).setDebugEnabled(false);
        }
        /**
         * 设置为高敏感度（更容易触发警告）
         */


        setHighSensitivity() {
          this.positionJumpThreshold = 5;
          this.singleFrameMoveThreshold = 30;
          this.accelerationThreshold = 1000;
          this.frameTimeThreshold = 16;
          this.updateThresholds();
        }
        /**
         * 设置为低敏感度（不容易触发警告）
         */


        setLowSensitivity() {
          this.positionJumpThreshold = 20;
          this.singleFrameMoveThreshold = 100;
          this.accelerationThreshold = 5000;
          this.frameTimeThreshold = 33;
          this.updateThresholds();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "enableDebug", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "positionJumpThreshold", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 10;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "singleFrameMoveThreshold", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 50;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "accelerationThreshold", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 2000;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "frameTimeThreshold", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 20;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ad932ae6060163325f66fc0879a5c8b4728f4179.js.map