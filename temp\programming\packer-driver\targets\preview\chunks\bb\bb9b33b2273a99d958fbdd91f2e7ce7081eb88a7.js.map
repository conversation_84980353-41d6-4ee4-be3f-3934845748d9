{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts"], "names": ["_decorator", "Vec2", "CCFloat", "CCInteger", "Enum", "eOrientationType", "ccclass", "property", "eSamplingStrategy", "ePathType", "PathPoint", "type", "displayName", "range", "slide", "tooltip", "visible", "orientationType", "Fixed", "Rotate", "isSubdivided", "_isSubdivided", "value", "constructor", "x", "y", "position", "fromJSON", "data", "smoothness", "speed", "stayDuration", "orientationParam", "PathData", "editor<PERSON><PERSON><PERSON>", "pathType", "Custom", "_cachedSubdividedPoints", "catmullRomPoint", "t", "p0", "p1", "p2", "p3", "lerp", "t2", "t3", "catmullRom", "linear", "getStartPoint", "startIdx", "points", "length", "getSubdividedPoints", "regen", "generateSubdividedPointsInternal", "effectivePoints", "getPoints", "samplingStrategy", "UniformDistance", "generateUniformDistancePoints", "AdaptiveSubdivision", "generateAdaptiveSubdivisionPoints", "subdivided", "pointCount", "segmentCount", "closed", "push", "targetDistance", "calculateOptimalSamplingDistance", "i", "getControlPoint", "point", "pointNext", "startSmoothness", "endSmoothness", "segmentPoints", "sampleLineSegmentUniformly", "sampleCurveSegmentUniformly", "firstPoint", "lastPoint", "distance", "pop", "smoothSpeedTransitions", "totalSpeed", "avgSpeed", "targetFPS", "targetTimeInterval", "baseDistance", "Math", "max", "min", "point1", "point2", "result", "startPos", "endPos", "totalDistance", "ceil", "pos", "newPoint", "smoothing<PERSON><PERSON>ow", "originalSpeeds", "map", "p", "sum", "count", "j", "avgSmoothness", "estimatedLength", "estimateCurveLength", "tValues", "generateArcLengthParameterization", "createCurveInterpolatedPoint", "samples", "prevPos", "currentPos", "targetSegmentLength", "<PERSON><PERSON><PERSON><PERSON>", "currentT", "stepSize", "segment", "targetLength", "adaptiveSubdivision", "startIndex", "endIndex", "endIdx", "slice", "max<PERSON><PERSON><PERSON>", "subdivisionPoints", "subdivideRecursive", "t1", "depth", "tMid", "midPos", "linearMid", "error", "curvature", "calculateCurvature", "baseThreshold", "curvatureT<PERSON>eshold", "leftPoints", "rightPoints", "v1", "subtract", "v2", "len1", "len2", "normalize", "dot", "clampedDot", "angle", "acos", "PI", "index", "wrappedIndex", "add", "toJSON", "name", "pathData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;mCAElBQ,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;2BAKAC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;AAKZ;AACA;AACA;;;2BAEaC,S,WADZJ,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRL,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRL,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,KAAK,EAAE,IAA5D;AAAkEC,QAAAA,OAAO,EAAE;AAA3E,OAAD,C,UAGRR,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,IAAhC;AAAsCG,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAGRR,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,UAGRR,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEP,IAAI;AAAA;AAAA,iDAAZ;AAAgCQ,QAAAA,WAAW,EAAE,MAA7C;AAAqDG,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAGRR,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE,aAAjD;;AACNC,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKC,eAAL,KAAyB;AAAA;AAAA,oDAAiBC,KAA1C,IAAmD,KAAKD,eAAL,KAAyB;AAAA;AAAA,oDAAiBE,MAApG;AACH;;AAJK,OAAD,C,2BApBb,MACaT,SADb,CACuB;AA6BI,YAAZU,YAAY,GAAY;AAC/B,iBAAO,KAAKC,aAAZ;AACH;;AACsB,YAAZD,YAAY,CAACE,KAAD,EAAiB;AACpC,eAAKD,aAAL,GAAqBC,KAArB;AACH;;AAEDC,QAAAA,WAAW,CAACC,CAAD,EAAgBC,CAAhB,EAA+B;AAAA,cAA9BD,CAA8B;AAA9BA,YAAAA,CAA8B,GAAlB,CAAkB;AAAA;;AAAA,cAAfC,CAAe;AAAfA,YAAAA,CAAe,GAAH,CAAG;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAT1C;AAS0C,eARlCJ,aAQkC,GART,KAQS;AACtC,eAAKG,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACH;;AAEkB,YAARC,QAAQ,GAAS;AACxB,iBAAO,IAAIzB,IAAJ,CAAS,KAAKuB,CAAd,EAAiB,KAAKC,CAAtB,CAAP;AACH;;AAEkB,YAARC,QAAQ,CAACJ,KAAD,EAAc;AAC7B,eAAKE,CAAL,GAASF,KAAK,CAACE,CAAf;AACA,eAAKC,CAAL,GAASH,KAAK,CAACG,CAAf;AACH;;AAEME,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AAC7B,eAAKJ,CAAL,GAASI,IAAI,CAACJ,CAAL,IAAU,CAAnB;AACA,eAAKC,CAAL,GAASG,IAAI,CAACH,CAAL,IAAU,CAAnB;AACA,eAAKI,UAAL,GAAkBD,IAAI,CAACC,UAAL,IAAmB,CAArC;AACA,eAAKC,KAAL,GAAaF,IAAI,CAACE,KAAL,IAAc,GAA3B;AACA,eAAKC,YAAL,GAAoBH,IAAI,CAACG,YAAL,IAAqB,CAAzC;AACA,eAAKd,eAAL,GAAuBW,IAAI,CAACX,eAAL,IAAwB,CAA/C;AACA,eAAKe,gBAAL,GAAwBJ,IAAI,CAACI,gBAAL,IAAyB,CAAjD;AACH;;AA1DkB,O;;;;;iBAEA,C;;;;;;;iBAGA,C;;;;;;;iBAGS,C;;;;;;;iBAGL,G;;;;;;;iBAGO,C;;;;;;;iBAGa,C;;;;;;;iBAQT,C;;;AAoCtC;AACA;AACA;;;0BAEaC,Q,YADZ3B,OAAO,CAAC,UAAD,C,WAEHC,QAAQ,CAAC;AAAEK,QAAAA,WAAW,EAAE,MAAf;AAAuBsB,QAAAA,UAAU,EAAE;AAAnC,OAAD,C,WAGR3B,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEP,IAAI,CAACK,SAAD,CAAZ;AAAyBG,QAAAA,WAAW,EAAE;AAAtC,OAAD,C,WAGRL,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,UAAhC;;AAA4CI,QAAAA,OAAO,GAAG;AAC5D;AACA,iBAAO,KAAKmB,QAAL,KAAkB1B,SAAS,CAAC2B,MAAnC;AACH;;AAHS,OAAD,C,WAMR7B,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,iBAAhC;;AAAmDI,QAAAA,OAAO,GAAG;AACnE;AACA,iBAAO,KAAKmB,QAAL,KAAkB1B,SAAS,CAAC2B,MAAnC;AACH;;AAHS,OAAD,C,WAMR7B,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAE,CAACD,SAAD,CAAR;AAAqBE,QAAAA,WAAW,EAAE,KAAlC;;AAAyCI,QAAAA,OAAO,GAAG;AACzD;AACA,iBAAO,KAAKmB,QAAL,KAAkB1B,SAAS,CAAC2B,MAAnC;AACH;;AAHS,OAAD,C,WAMR7B,QAAQ,CAAC;AAAEK,QAAAA,WAAW,EAAE,QAAf;AAAyBG,QAAAA,OAAO,EAAE,UAAlC;;AAA8CC,QAAAA,OAAO,GAAG;AAC9D;AACA,iBAAO,KAAKmB,QAAL,KAAkB1B,SAAS,CAAC2B,MAAnC;AACH;;AAHS,OAAD,C,WAMR7B,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEP,IAAI,CAACI,iBAAD,CAAZ;AAAiCI,QAAAA,WAAW,EAAE,MAA9C;AAAsDG,QAAAA,OAAO,EAAE,SAA/D;;AAA0EC,QAAAA,OAAO,GAAG;AAC1F;AACA,iBAAO,KAAKmB,QAAL,KAAkB1B,SAAS,CAAC2B,MAAnC;AACH;;AAHS,OAAD,C,6BAhCb,MACaH,QADb,CACsB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAqClB;AArCkB,eAsCVI,uBAtCU,GAsCoC,IAtCpC;AAAA;;AAwClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACiC,eAAfC,eAAe,CAACC,CAAD,EAAYC,EAAZ,EAAsBC,EAAtB,EAAgCC,EAAhC,EAA0CC,EAA1C,EAAoDd,UAApD,EAAoF;AAAA,cAAhCA,UAAgC;AAAhCA,YAAAA,UAAgC,GAAX,GAAW;AAAA;;AAC7G;AACA,cAAIA,UAAU,KAAK,CAAnB,EAAsB;AAClB,mBAAO5B,IAAI,CAAC2C,IAAL,CAAU,IAAI3C,IAAJ,EAAV,EAAsBwC,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAP;AACH;;AAED,cAAMM,EAAE,GAAGN,CAAC,GAAGA,CAAf;AACA,cAAMO,EAAE,GAAGD,EAAE,GAAGN,CAAhB,CAP6G,CAS7G;;AACA,cAAMQ,UAAU,GAAG,IAAI9C,IAAJ,EAAnB;AACA8C,UAAAA,UAAU,CAACvB,CAAX,GAAe,OACV,IAAIiB,EAAE,CAACjB,CAAR,GACA,CAAC,CAACgB,EAAE,CAAChB,CAAJ,GAAQkB,EAAE,CAAClB,CAAZ,IAAiBe,CADjB,GAEA,CAAC,IAAIC,EAAE,CAAChB,CAAP,GAAW,IAAIiB,EAAE,CAACjB,CAAlB,GAAsB,IAAIkB,EAAE,CAAClB,CAA7B,GAAiCmB,EAAE,CAACnB,CAArC,IAA0CqB,EAF1C,GAGA,CAAC,CAACL,EAAE,CAAChB,CAAJ,GAAQ,IAAIiB,EAAE,CAACjB,CAAf,GAAmB,IAAIkB,EAAE,CAAClB,CAA1B,GAA8BmB,EAAE,CAACnB,CAAlC,IAAuCsB,EAJ5B,CAAf;AAOAC,UAAAA,UAAU,CAACtB,CAAX,GAAe,OACV,IAAIgB,EAAE,CAAChB,CAAR,GACA,CAAC,CAACe,EAAE,CAACf,CAAJ,GAAQiB,EAAE,CAACjB,CAAZ,IAAiBc,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACf,CAAP,GAAW,IAAIgB,EAAE,CAAChB,CAAlB,GAAsB,IAAIiB,EAAE,CAACjB,CAA7B,GAAiCkB,EAAE,CAAClB,CAArC,IAA0CoB,EAF1C,GAGA,CAAC,CAACL,EAAE,CAACf,CAAJ,GAAQ,IAAIgB,EAAE,CAAChB,CAAf,GAAmB,IAAIiB,EAAE,CAACjB,CAA1B,GAA8BkB,EAAE,CAAClB,CAAlC,IAAuCqB,EAJ5B,CAAf,CAlB6G,CAyB7G;;AACA,cAAIjB,UAAU,GAAG,CAAjB,EAAoB;AAChB,gBAAMmB,MAAM,GAAG/C,IAAI,CAAC2C,IAAL,CAAU,IAAI3C,IAAJ,EAAV,EAAsBwC,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAf;AACA,mBAAOtC,IAAI,CAAC2C,IAAL,CAAU,IAAI3C,IAAJ,EAAV,EAAsB+C,MAAtB,EAA8BD,UAA9B,EAA0ClB,UAA1C,CAAP;AACH;;AAED,iBAAOkB,UAAP;AACH;;AAEME,QAAAA,aAAa,GAAmB;AACnC,cAAI,KAAKC,QAAL,GAAgB,CAAhB,IAAqB,KAAKA,QAAL,IAAiB,KAAKC,MAAL,CAAYC,MAAtD,EAA8D;AAC1D,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKD,MAAL,CAAY,KAAKD,QAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWG,QAAAA,mBAAmB,CAACC,KAAD,EAAsC;AAAA,cAArCA,KAAqC;AAArCA,YAAAA,KAAqC,GAApB,KAAoB;AAAA;;AAC5D,cAAK,CAAC,KAAKjB,uBAAN,IAAiC,KAAKA,uBAAL,CAA6Be,MAA7B,KAAwC,CAA1E,IAAgFE,KAApF,EAA2F;AACvF,iBAAKjB,uBAAL,GAA+B,KAAKkB,gCAAL,EAA/B;AACH;;AAED,iBAAO,KAAKlB,uBAAZ;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,gCAAgC,GAAgB;AACpD,cAAMC,eAAe,GAAG,KAAKC,SAAL,EAAxB;;AAEA,cAAID,eAAe,CAACJ,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,mBAAOI,eAAP;AACH,WALmD,CAOpD;;;AACA,kBAAQ,KAAKE,gBAAb;AACI,iBAAKlD,iBAAiB,CAACmD,eAAvB;AACI,qBAAO,KAAKC,6BAAL,CAAmCJ,eAAnC,CAAP;;AACJ,iBAAKhD,iBAAiB,CAACqD,mBAAvB;AACI,qBAAO,KAAKC,iCAAL,CAAuCN,eAAvC,CAAP;;AACJ;AACI,qBAAO,KAAKI,6BAAL,CAAmCJ,eAAnC,CAAP;AANR;AAQH;AAED;AACJ;AACA;;;AACYI,QAAAA,6BAA6B,CAACJ,eAAD,EAA4C;AAC7E,cAAMO,UAAuB,GAAG,EAAhC;AACA,cAAMC,UAAU,GAAGR,eAAe,CAACJ,MAAnC;AACA,cAAMa,YAAY,GAAG,KAAKC,MAAL,GAAcF,UAAd,GAA2BA,UAAU,GAAG,CAA7D,CAH6E,CAK7E;;AACAD,UAAAA,UAAU,CAACI,IAAX,CAAgBX,eAAe,CAAC,CAAD,CAA/B,EAN6E,CAQ7E;;AACA,cAAMY,cAAc,GAAG,KAAKC,gCAAL,CAAsCb,eAAtC,CAAvB;;AAEA,eAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,YAApB,EAAkCK,CAAC,EAAnC,EAAuC;AACnC,gBAAM9B,EAAE,GAAG,KAAK+B,eAAL,CAAqBf,eAArB,EAAsCc,CAAC,GAAG,CAA1C,CAAX;AACA,gBAAM7B,EAAE,GAAGe,eAAe,CAACc,CAAD,CAAf,CAAmB5C,QAA9B;AACA,gBAAMgB,EAAE,GAAG,KAAK6B,eAAL,CAAqBf,eAArB,EAAsCc,CAAC,GAAG,CAA1C,CAAX;AACA,gBAAM3B,EAAE,GAAG,KAAK4B,eAAL,CAAqBf,eAArB,EAAsCc,CAAC,GAAG,CAA1C,CAAX;AAEA,gBAAME,KAAK,GAAGhB,eAAe,CAACc,CAAD,CAA7B;AACA,gBAAMG,SAAS,GAAGjB,eAAe,CAAC,CAACc,CAAC,GAAG,CAAL,IAAUN,UAAX,CAAjC;AAEA,gBAAMU,eAAe,GAAGF,KAAK,CAAC3C,UAA9B;AACA,gBAAM8C,aAAa,GAAGF,SAAS,CAAC5C,UAAhC;;AAEA,gBAAI6C,eAAe,KAAK,CAApB,IAAyBC,aAAa,KAAK,CAA/C,EAAkD;AAC9C;AACA,kBAAMC,aAAa,GAAG,KAAKC,0BAAL,CAAgCL,KAAhC,EAAuCC,SAAvC,EAAkDL,cAAlD,CAAtB;AACAL,cAAAA,UAAU,CAACI,IAAX,CAAgB,GAAGS,aAAnB;AACH,aAJD,MAIO;AACH;AACA,kBAAMA,cAAa,GAAG,KAAKE,2BAAL,CAClBtC,EADkB,EACdC,EADc,EACVC,EADU,EACNC,EADM,EACF6B,KADE,EACKC,SADL,EACgBL,cADhB,CAAtB;;AAGAL,cAAAA,UAAU,CAACI,IAAX,CAAgB,GAAGS,cAAnB;AACH;AACJ,WAlC4E,CAoC7E;;;AACA,cAAI,KAAKV,MAAL,IAAeH,UAAU,CAACX,MAAX,GAAoB,CAAvC,EAA0C;AACtC,gBAAM2B,UAAU,GAAGhB,UAAU,CAAC,CAAD,CAA7B;AACA,gBAAMiB,SAAS,GAAGjB,UAAU,CAACA,UAAU,CAACX,MAAX,GAAoB,CAArB,CAA5B;AACA,gBAAM6B,QAAQ,GAAGhF,IAAI,CAACgF,QAAL,CAAcF,UAAU,CAACrD,QAAzB,EAAmCsD,SAAS,CAACtD,QAA7C,CAAjB;;AAEA,gBAAIuD,QAAQ,GAAG,GAAf,EAAoB;AAChBlB,cAAAA,UAAU,CAACmB,GAAX;AACH;AACJ,WA7C4E,CA+C7E;;;AACA,eAAKC,sBAAL,CAA4BpB,UAA5B;AAEA,iBAAOA,UAAP;AACH;AAED;AACJ;AACA;;;AACYM,QAAAA,gCAAgC,CAACb,eAAD,EAAuC;AAC3E;AACA,cAAI4B,UAAU,GAAG,CAAjB;;AACA,eAAK,IAAMZ,KAAX,IAAoBhB,eAApB,EAAqC;AACjC4B,YAAAA,UAAU,IAAIZ,KAAK,CAAC1C,KAApB;AACH;;AACD,cAAMuD,QAAQ,GAAGD,UAAU,GAAG5B,eAAe,CAACJ,MAA9C,CAN2E,CAQ3E;AACA;;AACA,cAAMkC,SAAS,GAAG,EAAlB;AACA,cAAMC,kBAAkB,GAAG,IAAID,SAA/B,CAX2E,CAWjC;AAE1C;;AACA,cAAME,YAAY,GAAIH,QAAQ,GAAGE,kBAAZ,GAAkC,IAAvD,CAd2E,CAcd;AAE7D;;AACA,iBAAOE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,EAAT,EAAaH,YAAb,CAAZ,CAAP;AACH;AAED;AACJ;AACA;;;AACYX,QAAAA,0BAA0B,CAACe,MAAD,EAAoBC,MAApB,EAAuCzB,cAAvC,EAA4E;AAC1G,cAAM0B,MAAmB,GAAG,EAA5B;AACA,cAAMC,QAAQ,GAAGH,MAAM,CAAClE,QAAxB;AACA,cAAMsE,MAAM,GAAGH,MAAM,CAACnE,QAAtB;AACA,cAAMuE,aAAa,GAAGhG,IAAI,CAACgF,QAAL,CAAcc,QAAd,EAAwBC,MAAxB,CAAtB;;AAEA,cAAIC,aAAa,IAAI7B,cAArB,EAAqC;AACjC;AACA0B,YAAAA,MAAM,CAAC3B,IAAP,CAAY0B,MAAZ;AACA,mBAAOC,MAAP;AACH;;AAED,cAAM7B,YAAY,GAAGwB,IAAI,CAACS,IAAL,CAAUD,aAAa,GAAG7B,cAA1B,CAArB;;AAEA,eAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIL,YAArB,EAAmCK,CAAC,EAApC,EAAwC;AACpC,gBAAM/B,CAAC,GAAG+B,CAAC,GAAGL,YAAd;AACA,gBAAMkC,GAAG,GAAGlG,IAAI,CAAC2C,IAAL,CAAU,IAAI3C,IAAJ,EAAV,EAAsB8F,QAAtB,EAAgCC,MAAhC,EAAwCzD,CAAxC,CAAZ;;AAEA,gBAAI+B,CAAC,KAAKL,YAAV,EAAwB;AACpB;AACA6B,cAAAA,MAAM,CAAC3B,IAAP,CAAY0B,MAAZ;AACH,aAHD,MAGO;AACH;AACA,kBAAMO,QAAQ,GAAG,IAAI1F,SAAJ,CAAcyF,GAAG,CAAC3E,CAAlB,EAAqB2E,GAAG,CAAC1E,CAAzB,CAAjB;AACA2E,cAAAA,QAAQ,CAACtE,KAAT,GAAiB8D,MAAM,CAAC9D,KAAP,GAAe,CAAC+D,MAAM,CAAC/D,KAAP,GAAe8D,MAAM,CAAC9D,KAAvB,IAAgCS,CAAhE;AACA6D,cAAAA,QAAQ,CAACvE,UAAT,GAAsB+D,MAAM,CAAC/D,UAAP,GAAoB,CAACgE,MAAM,CAAChE,UAAP,GAAoB+D,MAAM,CAAC/D,UAA5B,IAA0CU,CAApF;AACA6D,cAAAA,QAAQ,CAACnF,eAAT,GAA2B2E,MAAM,CAAC3E,eAAlC;AACAmF,cAAAA,QAAQ,CAACpE,gBAAT,GAA4B4D,MAAM,CAAC5D,gBAAP,GAA0B,CAAC6D,MAAM,CAAC7D,gBAAP,GAA0B4D,MAAM,CAAC5D,gBAAlC,IAAsDO,CAA5G;AACA6D,cAAAA,QAAQ,CAAChF,YAAT,GAAwB,IAAxB;AACA0E,cAAAA,MAAM,CAAC3B,IAAP,CAAYiC,QAAZ;AACH;AACJ;;AAED,iBAAON,MAAP;AACH;AAED;AACJ;AACA;;;AACYX,QAAAA,sBAAsB,CAAChC,MAAD,EAA4B;AACtD,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB,OAD+B,CAGtD;;AACA,cAAMiD,eAAe,GAAG,CAAxB;AACA,cAAMC,cAAc,GAAGnD,MAAM,CAACoD,GAAP,CAAWC,CAAC,IAAIA,CAAC,CAAC1E,KAAlB,CAAvB;;AAEA,eAAK,IAAIwC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGnB,MAAM,CAACC,MAAP,GAAgB,CAApC,EAAuCkB,CAAC,EAAxC,EAA4C;AACxC,gBAAImC,GAAG,GAAG,CAAV;AACA,gBAAIC,KAAK,GAAG,CAAZ;;AAEA,iBAAK,IAAIC,CAAC,GAAGlB,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYpB,CAAC,GAAG+B,eAAhB,CAAb,EAA+CM,CAAC,IAAIlB,IAAI,CAACE,GAAL,CAASxC,MAAM,CAACC,MAAP,GAAgB,CAAzB,EAA4BkB,CAAC,GAAG+B,eAAhC,CAApD,EAAsGM,CAAC,EAAvG,EAA2G;AACvGF,cAAAA,GAAG,IAAIH,cAAc,CAACK,CAAD,CAArB;AACAD,cAAAA,KAAK;AACR,aAPuC,CASxC;;;AACA,gBAAIvD,MAAM,CAACmB,CAAD,CAAN,CAAUlD,YAAd,EAA4B;AACxB+B,cAAAA,MAAM,CAACmB,CAAD,CAAN,CAAUxC,KAAV,GAAkB2E,GAAG,GAAGC,KAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACY5B,QAAAA,2BAA2B,CAC/BtC,EAD+B,EACrBC,EADqB,EACXC,EADW,EACDC,EADC,EAE/BiD,MAF+B,EAEZC,MAFY,EAG/BzB,cAH+B,EAIpB;AACX,cAAM0B,MAAmB,GAAG,EAA5B;AACA,cAAMc,aAAa,GAAG,CAAChB,MAAM,CAAC/D,UAAP,GAAoBgE,MAAM,CAAChE,UAA5B,IAA0C,CAAhE,CAFW,CAIX;;AACA,cAAMgF,eAAe,GAAG,KAAKC,mBAAL,CAAyBtE,EAAzB,EAA6BC,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCiE,aAAzC,CAAxB;;AAEA,cAAIC,eAAe,IAAIzC,cAAvB,EAAuC;AACnC0B,YAAAA,MAAM,CAAC3B,IAAP,CAAY0B,MAAZ;AACA,mBAAOC,MAAP;AACH;;AAED,cAAM7B,YAAY,GAAGwB,IAAI,CAACS,IAAL,CAAUW,eAAe,GAAGzC,cAA5B,CAArB,CAZW,CAcX;;AACA,cAAM2C,OAAO,GAAG,KAAKC,iCAAL,CAAuCxE,EAAvC,EAA2CC,EAA3C,EAA+CC,EAA/C,EAAmDC,EAAnD,EAAuDiE,aAAvD,EAAsE3C,YAAtE,CAAhB;;AAEA,eAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIL,YAArB,EAAmCK,CAAC,EAApC,EAAwC;AACpC,gBAAM/B,CAAC,GAAGwE,OAAO,CAACzC,CAAD,CAAjB;;AAEA,gBAAIA,CAAC,KAAKL,YAAV,EAAwB;AACpB;AACA6B,cAAAA,MAAM,CAAC3B,IAAP,CAAY0B,MAAZ;AACH,aAHD,MAGO;AACH;AACA,kBAAMO,QAAQ,GAAGnE,QAAQ,CAACgF,4BAAT,CAAsCzE,EAAtC,EAA0CC,EAA1C,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsDiD,MAAtD,EAA8DC,MAA9D,EAAsEtD,CAAtE,EAAyEqE,aAAzE,CAAjB;AACAd,cAAAA,MAAM,CAAC3B,IAAP,CAAYiC,QAAZ;AACH;AACJ;;AAED,iBAAON,MAAP;AACH;AAED;AACJ;AACA;;;AACYgB,QAAAA,mBAAmB,CAACtE,EAAD,EAAWC,EAAX,EAAqBC,EAArB,EAA+BC,EAA/B,EAAyCd,UAAzC,EAAqE;AAC5F,cAAIuB,MAAM,GAAG,CAAb;AACA,cAAM8D,OAAO,GAAG,EAAhB,CAF4F,CAExE;;AACpB,cAAIC,OAAO,GAAGlF,QAAQ,CAACK,eAAT,CAAyB,CAAzB,EAA4BE,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4Cd,UAA5C,CAAd;;AAEA,eAAK,IAAIyC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI4C,OAArB,EAA8B5C,CAAC,EAA/B,EAAmC;AAC/B,gBAAM/B,CAAC,GAAG+B,CAAC,GAAG4C,OAAd;AACA,gBAAME,UAAU,GAAGnF,QAAQ,CAACK,eAAT,CAAyBC,CAAzB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4Cd,UAA5C,CAAnB;AACAuB,YAAAA,MAAM,IAAInD,IAAI,CAACgF,QAAL,CAAckC,OAAd,EAAuBC,UAAvB,CAAV;AACAD,YAAAA,OAAO,GAAGC,UAAV;AACH;;AAED,iBAAOhE,MAAP;AACH;AAED;AACJ;AACA;;;AACY4D,QAAAA,iCAAiC,CACrCxE,EADqC,EAC3BC,EAD2B,EACjBC,EADiB,EACPC,EADO,EAErCd,UAFqC,EAEjBoC,YAFiB,EAG7B;AACR,cAAM8C,OAAiB,GAAG,CAAC,CAAD,CAA1B,CADQ,CACuB;;AAC/B,cAAMM,mBAAmB,GAAG,KAAKP,mBAAL,CAAyBtE,EAAzB,EAA6BC,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCd,UAAzC,IAAuDoC,YAAnF;AAEA,cAAIqD,aAAa,GAAG,CAApB;AACA,cAAIC,QAAQ,GAAG,CAAf;AACA,cAAIJ,OAAO,GAAGlF,QAAQ,CAACK,eAAT,CAAyB,CAAzB,EAA4BE,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4Cd,UAA5C,CAAd;AAEA,cAAM2F,QAAQ,GAAG,KAAjB,CARQ,CAQgB;;AAExB,eAAK,IAAIC,OAAO,GAAG,CAAnB,EAAsBA,OAAO,IAAIxD,YAAjC,EAA+CwD,OAAO,EAAtD,EAA0D;AACtD,gBAAMC,YAAY,GAAGD,OAAO,GAAGJ,mBAA/B,CADsD,CAGtD;;AACA,mBAAOC,aAAa,GAAGI,YAAhB,IAAgCH,QAAQ,GAAG,CAAlD,EAAqD;AACjDA,cAAAA,QAAQ,IAAIC,QAAZ;AACA,kBAAMJ,UAAU,GAAGnF,QAAQ,CAACK,eAAT,CAAyBiF,QAAzB,EAAmC/E,EAAnC,EAAuCC,EAAvC,EAA2CC,EAA3C,EAA+CC,EAA/C,EAAmDd,UAAnD,CAAnB;AACAyF,cAAAA,aAAa,IAAIrH,IAAI,CAACgF,QAAL,CAAckC,OAAd,EAAuBC,UAAvB,CAAjB;AACAD,cAAAA,OAAO,GAAGC,UAAV;AACH;;AAEDL,YAAAA,OAAO,CAAC5C,IAAR,CAAasB,IAAI,CAACE,GAAL,CAAS4B,QAAT,EAAmB,CAAnB,CAAb;AACH;;AAED,iBAAOR,OAAP;AACH;AAED;AACJ;AACA;;;AACYjD,QAAAA,iCAAiC,CAACN,eAAD,EAA4C;AACjF,cAAMO,UAAuB,GAAG,EAAhC;AACA,cAAMC,UAAU,GAAGR,eAAe,CAACJ,MAAnC,CAFiF,CAIjF;;AACAW,UAAAA,UAAU,CAACI,IAAX,CAAgBX,eAAe,CAAC,CAAD,CAA/B,EALiF,CAOjF;;AACA,cAAMS,YAAY,GAAG,KAAKC,MAAL,GAAcF,UAAd,GAA2BA,UAAU,GAAG,CAA7D,CARiF,CAUjF;;AACA,eAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,YAApB,EAAkCK,CAAC,EAAnC,EAAuC;AACnC,gBAAM9B,EAAE,GAAG,KAAK+B,eAAL,CAAqBf,eAArB,EAAsCc,CAAC,GAAG,CAA1C,CAAX;AACA,gBAAM7B,EAAE,GAAGe,eAAe,CAACc,CAAD,CAAf,CAAmB5C,QAA9B;AACA,gBAAMgB,EAAE,GAAG,KAAK6B,eAAL,CAAqBf,eAArB,EAAsCc,CAAC,GAAG,CAA1C,CAAX;AACA,gBAAM3B,EAAE,GAAG,KAAK4B,eAAL,CAAqBf,eAArB,EAAsCc,CAAC,GAAG,CAA1C,CAAX;AAEA,gBAAME,KAAK,GAAGhB,eAAe,CAACc,CAAD,CAA7B;AACA,gBAAMG,SAAS,GAAGjB,eAAe,CAAC,CAACc,CAAC,GAAG,CAAL,IAAUN,UAAX,CAAjC;AAEA,gBAAMU,eAAe,GAAGF,KAAK,CAAC3C,UAA9B;AACA,gBAAM8C,aAAa,GAAGF,SAAS,CAAC5C,UAAhC,CAVmC,CAYnC;;AACA,gBAAI6C,eAAe,KAAK,CAApB,IAAyBC,aAAa,KAAK,CAA/C,EAAkD;AAC9C;AACAZ,cAAAA,UAAU,CAACI,IAAX,CAAgBM,SAAhB;AACH,aAHD,MAGO;AACH;AACA,kBAAMG,aAAa,GAAG,KAAK+C,mBAAL,CAAyBnF,EAAzB,EAA6BC,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyC6B,KAAzC,EAAgDC,SAAhD,CAAtB;AACAV,cAAAA,UAAU,CAACI,IAAX,CAAgB,GAAGS,aAAnB;AACH;AACJ,WAhCgF,CAkCjF;;;AACA,cAAI,KAAKV,MAAL,IAAeH,UAAU,CAACX,MAAX,GAAoB,CAAvC,EAA0C;AACtC,gBAAM2B,UAAU,GAAGhB,UAAU,CAAC,CAAD,CAA7B;AACA,gBAAMiB,SAAS,GAAGjB,UAAU,CAACA,UAAU,CAACX,MAAX,GAAoB,CAArB,CAA5B;AACA,gBAAM6B,QAAQ,GAAGhF,IAAI,CAACgF,QAAL,CAAcF,UAAU,CAACrD,QAAzB,EAAmCsD,SAAS,CAACtD,QAA7C,CAAjB;;AAEA,gBAAIuD,QAAQ,GAAG,GAAf,EAAoB;AAChBlB,cAAAA,UAAU,CAACmB,GAAX;AACH;AACJ;;AAED,iBAAOnB,UAAP;AACH;AAED;AACJ;AACA;;;AACYN,QAAAA,SAAS,GAAgB;AAC7B,cAAI,KAAKN,MAAL,CAAYC,MAAZ,KAAuB,CAA3B,EAA8B,OAAO,EAAP;AAE9B,cAAMwE,UAAU,GAAGnC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,KAAKzC,QAAd,EAAwB,KAAKC,MAAL,CAAYC,MAAZ,GAAqB,CAA7C,CAAZ,CAAnB;AACA,cAAMyE,QAAQ,GAAG,KAAKC,MAAL,KAAgB,CAAC,CAAjB,GAAqB,KAAK3E,MAAL,CAAYC,MAAZ,GAAqB,CAA1C,GAA8CqC,IAAI,CAACC,GAAL,CAASkC,UAAT,EAAqBnC,IAAI,CAACE,GAAL,CAAS,KAAKmC,MAAd,EAAsB,KAAK3E,MAAL,CAAYC,MAAZ,GAAqB,CAA3C,CAArB,CAA/D;AAEA,iBAAO,KAAKD,MAAL,CAAY4E,KAAZ,CAAkBH,UAAlB,EAA8BC,QAAQ,GAAG,CAAzC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACYF,QAAAA,mBAAmB,CACvBnF,EADuB,EACbC,EADa,EACHC,EADG,EACOC,EADP,EAEvBiD,MAFuB,EAEJC,MAFI,EAGvBmC,QAHuB,EAIZ;AAAA,cADXA,QACW;AADXA,YAAAA,QACW,GADQ,CACR;AAAA;;AACX,cAAMpB,aAAa,GAAG,CAAChB,MAAM,CAAC/D,UAAP,GAAoBgE,MAAM,CAAChE,UAA5B,IAA0C,CAAhE,CADW,CAGX;;AACA,cAAI+E,aAAa,KAAK,CAAtB,EAAyB;AACrB,mBAAO,CAACf,MAAD,CAAP;AACH,WANU,CAQX;;;AACA,cAAMoC,iBAAiB,GAAGhG,QAAQ,CAACiG,kBAAT,CAA4B1F,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CiD,MAA5C,EAAoDC,MAApD,EAA4D,CAA5D,EAA+D,QAA/D,EAAyE,CAAzE,EAA4EmC,QAA5E,EAAsFpB,aAAtF,CAA1B,CATW,CAWX;;AACAqB,UAAAA,iBAAiB,CAAC9D,IAAlB,CAAuB0B,MAAvB;AAEA,iBAAOoC,iBAAP;AACH;AAED;AACJ;AACA;;;AAC6B,eAAlBC,kBAAkB,CACrB1F,EADqB,EACXC,EADW,EACDC,EADC,EACSC,EADT,EAErBiD,MAFqB,EAEFC,MAFE,EAGrBsC,EAHqB,EAGTtF,EAHS,EAGGuF,KAHH,EAGkBJ,QAHlB,EAGoCnG,UAHpC,EAIV;AACX;AACA,cAAIuG,KAAK,IAAIJ,QAAb,EAAuB;AACnB,mBAAO,CAAC/F,QAAQ,CAACgF,4BAAT,CAAsCzE,EAAtC,EAA0CC,EAA1C,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsDiD,MAAtD,EAA8DC,MAA9D,EAAsEhD,EAAtE,EAA0EhB,UAA1E,CAAD,CAAP;AACH;;AAED,cAAMwG,IAAI,GAAG,CAACF,EAAE,GAAGtF,EAAN,IAAY,CAAzB,CANW,CAQX;;AACA,cAAMkD,QAAQ,GAAG9D,QAAQ,CAACK,eAAT,CAAyB6F,EAAzB,EAA6B3F,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCC,EAAzC,EAA6Cd,UAA7C,CAAjB;AACA,cAAMyG,MAAM,GAAGrG,QAAQ,CAACK,eAAT,CAAyB+F,IAAzB,EAA+B7F,EAA/B,EAAmCC,EAAnC,EAAuCC,EAAvC,EAA2CC,EAA3C,EAA+Cd,UAA/C,CAAf;AACA,cAAMmE,MAAM,GAAG/D,QAAQ,CAACK,eAAT,CAAyBO,EAAzB,EAA6BL,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCC,EAAzC,EAA6Cd,UAA7C,CAAf,CAXW,CAaX;;AACA,cAAM0G,SAAS,GAAGtI,IAAI,CAAC2C,IAAL,CAAU,IAAI3C,IAAJ,EAAV,EAAsB8F,QAAtB,EAAgCC,MAAhC,EAAwC,GAAxC,CAAlB,CAdW,CAgBX;;AACA,cAAMwC,KAAK,GAAGvI,IAAI,CAACgF,QAAL,CAAcqD,MAAd,EAAsBC,SAAtB,CAAd,CAjBW,CAmBX;;AACA,cAAME,SAAS,GAAGxG,QAAQ,CAACyG,kBAAT,CAA4B3C,QAA5B,EAAsCuC,MAAtC,EAA8CtC,MAA9C,CAAlB,CApBW,CAsBX;;AACA,cAAMf,QAAQ,GAAGhF,IAAI,CAACgF,QAAL,CAAcc,QAAd,EAAwBC,MAAxB,CAAjB;AACA,cAAM2C,aAAa,GAAGlD,IAAI,CAACC,GAAL,CAAS,GAAT,EAAcT,QAAQ,GAAG,IAAzB,CAAtB,CAxBW,CAwB2C;;AACtD,cAAM2D,kBAAkB,GAAGD,aAAa,IAAI,IAAIF,SAAS,GAAG,EAApB,CAAxC,CAzBW,CAyBsD;AACjE;AACA;;AACA,cAAID,KAAK,GAAGI,kBAAZ,EAAgC;AAC5B,mBAAO,CAAC3G,QAAQ,CAACgF,4BAAT,CAAsCzE,EAAtC,EAA0CC,EAA1C,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsDiD,MAAtD,EAA8DC,MAA9D,EAAsEhD,EAAtE,EAA0EhB,UAA1E,CAAD,CAAP;AACH,WA9BU,CAgCX;;;AACA,cAAMgH,UAAU,GAAG5G,QAAQ,CAACiG,kBAAT,CAA4B1F,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CiD,MAA5C,EAAoDC,MAApD,EAA4DsC,EAA5D,EAAgEE,IAAhE,EAAsED,KAAK,GAAG,CAA9E,EAAiFJ,QAAjF,EAA2FnG,UAA3F,CAAnB;AACA,cAAMiH,WAAW,GAAG7G,QAAQ,CAACiG,kBAAT,CAA4B1F,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CiD,MAA5C,EAAoDC,MAApD,EAA4DwC,IAA5D,EAAkExF,EAAlE,EAAsEuF,KAAK,GAAG,CAA9E,EAAiFJ,QAAjF,EAA2FnG,UAA3F,CAApB;AAEA,iBAAO,CAAC,GAAGgH,UAAJ,EAAgB,GAAGC,WAAnB,CAAP;AACH;AAED;AACJ;AACA;;;AAC6B,eAAlBJ,kBAAkB,CAACjG,EAAD,EAAWC,EAAX,EAAqBC,EAArB,EAAuC;AAC5D,cAAMoG,EAAE,GAAG9I,IAAI,CAAC+I,QAAL,CAAc,IAAI/I,IAAJ,EAAd,EAA0ByC,EAA1B,EAA8BD,EAA9B,CAAX;AACA,cAAMwG,EAAE,GAAGhJ,IAAI,CAAC+I,QAAL,CAAc,IAAI/I,IAAJ,EAAd,EAA0B0C,EAA1B,EAA8BD,EAA9B,CAAX,CAF4D,CAI5D;;AACA,cAAMwG,IAAI,GAAGH,EAAE,CAAC3F,MAAH,EAAb;AACA,cAAM+F,IAAI,GAAGF,EAAE,CAAC7F,MAAH,EAAb;AACA,cAAI8F,IAAI,GAAG,KAAP,IAAgBC,IAAI,GAAG,KAA3B,EAAkC,OAAO,CAAP;AAElCJ,UAAAA,EAAE,CAACK,SAAH;AACAH,UAAAA,EAAE,CAACG,SAAH,GAV4D,CAY5D;;AACA,cAAMC,GAAG,GAAGpJ,IAAI,CAACoJ,GAAL,CAASN,EAAT,EAAaE,EAAb,CAAZ;AACA,cAAMK,UAAU,GAAG7D,IAAI,CAACC,GAAL,CAAS,CAAC,CAAV,EAAaD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAY0D,GAAZ,CAAb,CAAnB;AACA,cAAME,KAAK,GAAG9D,IAAI,CAAC+D,IAAL,CAAUF,UAAV,CAAd,CAf4D,CAiB5D;;AACA,iBAAOC,KAAK,GAAG9D,IAAI,CAACgE,EAApB;AACH;AAED;AACJ;AACA;;;AACuC,eAA5BxC,4BAA4B,CAC/BzE,EAD+B,EACrBC,EADqB,EACXC,EADW,EACDC,EADC,EAE/BiD,MAF+B,EAEZC,MAFY,EAG/BtD,CAH+B,EAGpBV,UAHoB,EAItB;AACT;AACA,cAAMsE,GAAG,GAAGlE,QAAQ,CAACK,eAAT,CAAyBC,CAAzB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4Cd,UAA5C,CAAZ;AACA,cAAMuE,QAAQ,GAAG,IAAI1F,SAAJ,CAAcyF,GAAG,CAAC3E,CAAlB,EAAqB2E,GAAG,CAAC1E,CAAzB,CAAjB,CAHS,CAKT;;AACA2E,UAAAA,QAAQ,CAACtE,KAAT,GAAiB8D,MAAM,CAAC9D,KAAP,GAAe,CAAC+D,MAAM,CAAC/D,KAAP,GAAe8D,MAAM,CAAC9D,KAAvB,IAAgCS,CAAhE;AACA6D,UAAAA,QAAQ,CAACvE,UAAT,GAAsB+D,MAAM,CAAC/D,UAAP,GAAoB,CAACgE,MAAM,CAAChE,UAAP,GAAoB+D,MAAM,CAAC/D,UAA5B,IAA0CU,CAApF;AACA6D,UAAAA,QAAQ,CAACnF,eAAT,GAA2B2E,MAAM,CAAC3E,eAAlC;AACAmF,UAAAA,QAAQ,CAACpE,gBAAT,GAA4B4D,MAAM,CAAC5D,gBAAP,GAA0B,CAAC6D,MAAM,CAAC7D,gBAAP,GAA0B4D,MAAM,CAAC5D,gBAAlC,IAAsDO,CAA5G;AACA6D,UAAAA,QAAQ,CAAChF,YAAT,GAAwB,IAAxB;AAEA,iBAAOgF,QAAP;AACH;AAED;AACJ;AACA;;;AACY7B,QAAAA,eAAe,CAACf,eAAD,EAA+BkG,KAA/B,EAAoD;AACvE,cAAM1F,UAAU,GAAGR,eAAe,CAACJ,MAAnC;;AAEA,cAAI,KAAKc,MAAT,EAAiB;AACb;AACA,gBAAMyF,YAAY,GAAG,CAAED,KAAK,GAAG1F,UAAT,GAAuBA,UAAxB,IAAsCA,UAA3D;AACA,mBAAOR,eAAe,CAACmG,YAAD,CAAf,CAA8BjI,QAArC;AACH,WAJD,MAIO;AACH;AACA,gBAAIgI,KAAK,GAAG,CAAZ,EAAe;AACX;AACA,kBAAMlH,EAAE,GAAGgB,eAAe,CAAC,CAAD,CAAf,CAAmB9B,QAA9B;AACA,kBAAMe,EAAE,GAAGe,eAAe,CAAC,CAAD,CAAf,CAAmB9B,QAA9B;AACA,qBAAOzB,IAAI,CAAC+I,QAAL,CAAc,IAAI/I,IAAJ,EAAd,EAA0BuC,EAA1B,EAA8BvC,IAAI,CAAC+I,QAAL,CAAc,IAAI/I,IAAJ,EAAd,EAA0BwC,EAA1B,EAA8BD,EAA9B,CAA9B,CAAP;AACH,aALD,MAKO,IAAIkH,KAAK,IAAI1F,UAAb,EAAyB;AAC5B;AACA,kBAAMxB,EAAE,GAAGgB,eAAe,CAACQ,UAAU,GAAG,CAAd,CAAf,CAAgCtC,QAA3C;AACA,kBAAMe,GAAE,GAAGe,eAAe,CAACQ,UAAU,GAAG,CAAd,CAAf,CAAgCtC,QAA3C;AACA,qBAAOzB,IAAI,CAAC2J,GAAL,CAAS,IAAI3J,IAAJ,EAAT,EAAqBwC,GAArB,EAAyBxC,IAAI,CAAC+I,QAAL,CAAc,IAAI/I,IAAJ,EAAd,EAA0BwC,GAA1B,EAA8BD,EAA9B,CAAzB,CAAP;AACH,aALM,MAKA;AACH,qBAAOgB,eAAe,CAACkG,KAAD,CAAf,CAAuBhI,QAA9B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACI;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACWmI,QAAAA,MAAM,GAAQ;AACjB,iBAAO;AACHC,YAAAA,IAAI,EAAE,KAAKA,IADR;AAEH5G,YAAAA,QAAQ,EAAE,KAAKA,QAFZ;AAGH4E,YAAAA,MAAM,EAAE,KAAKA,MAHV;AAIH3E,YAAAA,MAAM,EAAE,KAAKA,MAJV;AAKHe,YAAAA,MAAM,EAAE,KAAKA;AALV,WAAP;AAOH;AAED;AACJ;AACA;;;AACWvC,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AAC7B,cAAI,CAACA,IAAL,EAAW;AAEX,eAAKkI,IAAL,GAAYlI,IAAI,CAACkI,IAAL,IAAa,EAAzB;AACA,eAAK5G,QAAL,GAAgBtB,IAAI,CAACsB,QAAL,IAAiB,CAAjC;AACA,eAAK4E,MAAL,GAAclG,IAAI,CAACkG,MAAL,IAAe,CAAC,CAA9B;AACA,eAAK3E,MAAL,GAAcvB,IAAI,CAACuB,MAAL,GAAcvB,IAAI,CAACuB,MAAL,CAAYoD,GAAZ,CAAiBC,CAAD,IAAY;AACpD,gBAAMhC,KAAK,GAAG,IAAI9D,SAAJ,EAAd;AACA8D,YAAAA,KAAK,CAAC7C,QAAN,CAAe6E,CAAf;AACA,mBAAOhC,KAAP;AACH,WAJ2B,CAAd,GAIT,EAJL;AAKA,eAAKN,MAAL,GAActC,IAAI,CAACsC,MAAL,IAAe,KAA7B,CAX6B,CAa7B;;AACA,eAAK7B,uBAAL,GAA+B,IAA/B;AACH;AAED;AACJ;AACA;;;AAC0B,eAARV,QAAQ,CAACC,IAAD,EAAsB;AACxC,cAAMmI,QAAQ,GAAG,IAAI9H,QAAJ,EAAjB;AACA8H,UAAAA,QAAQ,CAACpI,QAAT,CAAkBC,IAAlB;AACA,iBAAOmI,QAAP;AACH;;AAhoBiB,O;;;;;iBAEI,E;;;;;;;iBAGOtJ,SAAS,CAAC2B,M;;;;;;;iBAMb,C;;;;;;;iBAMF,CAAC,C;;;;;;;iBAMI,E;;;;;;;iBAMJ,K;;;;;;;iBAMoB5B,iBAAiB,CAACmD,e", "sourcesContent": ["import { _decorator, Vec2, CCFloat, CCInteger, Enum } from 'cc';\r\nimport { eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable';\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eSamplingStrategy {\r\n    UniformDistance,    // 均匀距离采样（推荐，减少抖动）\r\n    AdaptiveSubdivision // 自适应细分（原有策略）\r\n}\r\n\r\nexport enum ePathType {\r\n    Custom,  // 自定义\r\n    Circle,  // 圆形(圆形不需要让策划再去编辑点了，程序来生成)\r\n}\r\n\r\n/**\r\n * 路径点数据\r\n */\r\n@ccclass(\"PathPoint\")\r\nexport class PathPoint {\r\n    @property({ type: CCFloat, displayName: \"X坐标\" })\r\n    public x: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"Y坐标\" })\r\n    public y: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], slide: true, tooltip: \"0=直线连接, 1=最大平滑曲线\" })\r\n    public smoothness: number = 1;\r\n\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    public speed: number = 500;\r\n\r\n    @property({ type: CCInteger, displayName: \"停留时间\", tooltip: \"飞机到达此点后停留时间（毫秒）\" })\r\n    public stayDuration: number = 0;\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public orientationType: eOrientationType = 0;\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"根据朝向类型不同而不同\",\r\n        visible() {\r\n            // @ts-ignore\r\n            return this.orientationType === eOrientationType.Fixed || this.orientationType === eOrientationType.Rotate;\r\n        }\r\n    })\r\n    public orientationParam: number = 0;\r\n\r\n    // 标记是否是插值的点（非原始点）\r\n    private _isSubdivided: boolean = false;\r\n    public get isSubdivided(): boolean {\r\n        return this._isSubdivided;\r\n    }\r\n    public set isSubdivided(value: boolean) {\r\n        this._isSubdivided = value;\r\n    }\r\n\r\n    constructor(x: number = 0, y: number = 0) {\r\n        this.x = x;\r\n        this.y = y;\r\n    }\r\n\r\n    public get position(): Vec2 {\r\n        return new Vec2(this.x, this.y);\r\n    }\r\n\r\n    public set position(value: Vec2) {\r\n        this.x = value.x;\r\n        this.y = value.y;\r\n    }\r\n\r\n    public fromJSON(data: any): void {\r\n        this.x = data.x || 0;\r\n        this.y = data.y || 0;\r\n        this.smoothness = data.smoothness || 1;\r\n        this.speed = data.speed || 500;\r\n        this.stayDuration = data.stayDuration || 0;\r\n        this.orientationType = data.orientationType || 0;\r\n        this.orientationParam = data.orientationParam || 0;\r\n    }\r\n}\r\n\r\n/**\r\n * 路径数据\r\n */\r\n@ccclass(\"PathData\")\r\nexport class PathData {\r\n    @property({ displayName: '路径名称', editorOnly: true })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: Enum(ePathType), displayName: \"路径类型\"})\r\n    public pathType: ePathType = ePathType.Custom;\r\n\r\n    @property({ type: CCInteger, displayName: '起始点(默认0)', visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public startIdx: number = 0;\r\n\r\n    @property({ type: CCInteger, displayName: '结束点(-1代表使用路径终点)', visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public endIdx: number = -1;\r\n\r\n    @property({ type: [PathPoint], displayName: '路径点', visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public points: PathPoint[] = [];\r\n\r\n    @property({ displayName: \"是否闭合路径\", tooltip: \"路径是否形成闭环\", visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public closed: boolean = false;\r\n\r\n    @property({ type: Enum(eSamplingStrategy), displayName: \"采样策略\", tooltip: \"路径点采样策略\", visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public samplingStrategy: eSamplingStrategy = eSamplingStrategy.UniformDistance;\r\n\r\n    // 缓存的路径数据（不参与序列化）\r\n    private _cachedSubdividedPoints: PathPoint[] | null = null;\r\n\r\n    /**\r\n     * 获取Catmull-Rom曲线上的点\r\n     * @param t 参数值 [0, 1]\r\n     * @param p0 前一个控制点（用于计算切线）\r\n     * @param p1 起始点（曲线经过此点）\r\n     * @param p2 结束点（曲线经过此点）\r\n     * @param p3 后一个控制点（用于计算切线）\r\n     * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线\r\n     */\r\n    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {\r\n        // 当smoothness为0时，直接返回线性插值（直线）\r\n        if (smoothness === 0) {\r\n            return Vec2.lerp(new Vec2(), p1, p2, t);\r\n        }\r\n\r\n        const t2 = t * t;\r\n        const t3 = t2 * t;\r\n\r\n        // 标准Catmull-Rom插值公式\r\n        const catmullRom = new Vec2();\r\n        catmullRom.x = 0.5 * (\r\n            (2 * p1.x) +\r\n            (-p0.x + p2.x) * t +\r\n            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +\r\n            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3\r\n        );\r\n\r\n        catmullRom.y = 0.5 * (\r\n            (2 * p1.y) +\r\n            (-p0.y + p2.y) * t +\r\n            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +\r\n            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3\r\n        );\r\n\r\n        // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合\r\n        if (smoothness < 1) {\r\n            const linear = Vec2.lerp(new Vec2(), p1, p2, t);\r\n            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);\r\n        }\r\n\r\n        return catmullRom;\r\n    }\r\n\r\n    public getStartPoint(): PathPoint|null {\r\n        if (this.startIdx < 0 || this.startIdx >= this.points.length) {\r\n            return null;\r\n        }\r\n        return this.points[this.startIdx];\r\n    }\r\n\r\n    /**\r\n     * 获取细分后的路径点（包含完整的PathPoint信息）\r\n     * 这是推荐的新方法，替代generateCurvePoints + 重新采样的方式\r\n     */\r\n    public getSubdividedPoints(regen: boolean = false): PathPoint[] {\r\n        if ((!this._cachedSubdividedPoints || this._cachedSubdividedPoints.length === 0) || regen) {\r\n            this._cachedSubdividedPoints = this.generateSubdividedPointsInternal();\r\n        }\r\n\r\n        return this._cachedSubdividedPoints!;\r\n    }\r\n\r\n    /**\r\n     * 内部方法：生成细分后的PathPoint数组\r\n     */\r\n    private generateSubdividedPointsInternal(): PathPoint[] {\r\n        const effectivePoints = this.getPoints();\r\n\r\n        if (effectivePoints.length < 2) {\r\n            return effectivePoints;\r\n        }\r\n\r\n        // 选择采样策略\r\n        switch (this.samplingStrategy) {\r\n            case eSamplingStrategy.UniformDistance:\r\n                return this.generateUniformDistancePoints(effectivePoints);\r\n            case eSamplingStrategy.AdaptiveSubdivision:\r\n                return this.generateAdaptiveSubdivisionPoints(effectivePoints);\r\n            default:\r\n                return this.generateUniformDistancePoints(effectivePoints);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 基于均匀距离的采样策略 - 推荐用于减少抖动\r\n     */\r\n    private generateUniformDistancePoints(effectivePoints: PathPoint[]): PathPoint[] {\r\n        const subdivided: PathPoint[] = [];\r\n        const pointCount = effectivePoints.length;\r\n        const segmentCount = this.closed ? pointCount : pointCount - 1;\r\n\r\n        // 添加第一个点\r\n        subdivided.push(effectivePoints[0]);\r\n\r\n        // 计算目标采样距离（基于速度和帧率）\r\n        const targetDistance = this.calculateOptimalSamplingDistance(effectivePoints);\r\n\r\n        for (let i = 0; i < segmentCount; i++) {\r\n            const p0 = this.getControlPoint(effectivePoints, i - 1);\r\n            const p1 = effectivePoints[i].position;\r\n            const p2 = this.getControlPoint(effectivePoints, i + 1);\r\n            const p3 = this.getControlPoint(effectivePoints, i + 2);\r\n\r\n            const point = effectivePoints[i];\r\n            const pointNext = effectivePoints[(i + 1) % pointCount];\r\n\r\n            const startSmoothness = point.smoothness;\r\n            const endSmoothness = pointNext.smoothness;\r\n\r\n            if (startSmoothness === 0 || endSmoothness === 0) {\r\n                // 直线连接：使用均匀距离采样\r\n                const segmentPoints = this.sampleLineSegmentUniformly(point, pointNext, targetDistance);\r\n                subdivided.push(...segmentPoints);\r\n            } else {\r\n                // 曲线：使用均匀距离采样\r\n                const segmentPoints = this.sampleCurveSegmentUniformly(\r\n                    p0, p1, p2, p3, point, pointNext, targetDistance\r\n                );\r\n                subdivided.push(...segmentPoints);\r\n            }\r\n        }\r\n\r\n        // 处理闭合路径的重复点\r\n        if (this.closed && subdivided.length > 1) {\r\n            const firstPoint = subdivided[0];\r\n            const lastPoint = subdivided[subdivided.length - 1];\r\n            const distance = Vec2.distance(firstPoint.position, lastPoint.position);\r\n\r\n            if (distance < 0.1) {\r\n                subdivided.pop();\r\n            }\r\n        }\r\n\r\n        // 后处理：平滑速度变化\r\n        this.smoothSpeedTransitions(subdivided);\r\n\r\n        return subdivided;\r\n    }\r\n\r\n    /**\r\n     * 计算最优采样距离\r\n     */\r\n    private calculateOptimalSamplingDistance(effectivePoints: PathPoint[]): number {\r\n        // 计算平均速度\r\n        let totalSpeed = 0;\r\n        for (const point of effectivePoints) {\r\n            totalSpeed += point.speed;\r\n        }\r\n        const avgSpeed = totalSpeed / effectivePoints.length;\r\n\r\n        // 基于速度和期望的时间间隔计算距离\r\n        // 假设60FPS，每帧移动的距离应该让移动看起来平滑\r\n        const targetFPS = 60;\r\n        const targetTimeInterval = 1 / targetFPS; // 约16.67ms\r\n\r\n        // 计算每帧期望移动的距离（像素）\r\n        const baseDistance = (avgSpeed * targetTimeInterval) / 1000; // 速度单位转换\r\n\r\n        // 限制在合理范围内：最小5像素，最大50像素\r\n        return Math.max(5, Math.min(50, baseDistance));\r\n    }\r\n\r\n    /**\r\n     * 均匀距离采样直线段\r\n     */\r\n    private sampleLineSegmentUniformly(point1: PathPoint, point2: PathPoint, targetDistance: number): PathPoint[] {\r\n        const result: PathPoint[] = [];\r\n        const startPos = point1.position;\r\n        const endPos = point2.position;\r\n        const totalDistance = Vec2.distance(startPos, endPos);\r\n\r\n        if (totalDistance <= targetDistance) {\r\n            // 距离太短，直接返回终点\r\n            result.push(point2);\r\n            return result;\r\n        }\r\n\r\n        const segmentCount = Math.ceil(totalDistance / targetDistance);\r\n\r\n        for (let i = 1; i <= segmentCount; i++) {\r\n            const t = i / segmentCount;\r\n            const pos = Vec2.lerp(new Vec2(), startPos, endPos, t);\r\n\r\n            if (i === segmentCount) {\r\n                // 最后一个点使用原始终点，保持所有属性\r\n                result.push(point2);\r\n            } else {\r\n                // 创建插值点\r\n                const newPoint = new PathPoint(pos.x, pos.y);\r\n                newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;\r\n                newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;\r\n                newPoint.orientationType = point1.orientationType;\r\n                newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;\r\n                newPoint.isSubdivided = true;\r\n                result.push(newPoint);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 平滑速度变化 - 后处理步骤\r\n     */\r\n    private smoothSpeedTransitions(points: PathPoint[]): void {\r\n        if (points.length < 3) return;\r\n\r\n        // 使用简单的移动平均来平滑速度变化\r\n        const smoothingWindow = 3;\r\n        const originalSpeeds = points.map(p => p.speed);\r\n\r\n        for (let i = 1; i < points.length - 1; i++) {\r\n            let sum = 0;\r\n            let count = 0;\r\n\r\n            for (let j = Math.max(0, i - smoothingWindow); j <= Math.min(points.length - 1, i + smoothingWindow); j++) {\r\n                sum += originalSpeeds[j];\r\n                count++;\r\n            }\r\n\r\n            // 只对插值点进行平滑，保持原始点的速度\r\n            if (points[i].isSubdivided) {\r\n                points[i].speed = sum / count;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 均匀距离采样曲线段\r\n     */\r\n    private sampleCurveSegmentUniformly(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        targetDistance: number\r\n    ): PathPoint[] {\r\n        const result: PathPoint[] = [];\r\n        const avgSmoothness = (point1.smoothness + point2.smoothness) / 2;\r\n\r\n        // 估算曲线长度（使用多个采样点）\r\n        const estimatedLength = this.estimateCurveLength(p0, p1, p2, p3, avgSmoothness);\r\n\r\n        if (estimatedLength <= targetDistance) {\r\n            result.push(point2);\r\n            return result;\r\n        }\r\n\r\n        const segmentCount = Math.ceil(estimatedLength / targetDistance);\r\n\r\n        // 使用弧长参数化进行均匀采样\r\n        const tValues = this.generateArcLengthParameterization(p0, p1, p2, p3, avgSmoothness, segmentCount);\r\n\r\n        for (let i = 1; i <= segmentCount; i++) {\r\n            const t = tValues[i];\r\n\r\n            if (i === segmentCount) {\r\n                // 最后一个点使用原始终点\r\n                result.push(point2);\r\n            } else {\r\n                // 创建插值点\r\n                const newPoint = PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t, avgSmoothness);\r\n                result.push(newPoint);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 估算曲线长度\r\n     */\r\n    private estimateCurveLength(p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number): number {\r\n        let length = 0;\r\n        const samples = 20; // 使用20个采样点估算长度\r\n        let prevPos = PathData.catmullRomPoint(0, p0, p1, p2, p3, smoothness);\r\n\r\n        for (let i = 1; i <= samples; i++) {\r\n            const t = i / samples;\r\n            const currentPos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);\r\n            length += Vec2.distance(prevPos, currentPos);\r\n            prevPos = currentPos;\r\n        }\r\n\r\n        return length;\r\n    }\r\n\r\n    /**\r\n     * 生成弧长参数化的t值数组\r\n     */\r\n    private generateArcLengthParameterization(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        smoothness: number, segmentCount: number\r\n    ): number[] {\r\n        const tValues: number[] = [0]; // 起始点t=0\r\n        const targetSegmentLength = this.estimateCurveLength(p0, p1, p2, p3, smoothness) / segmentCount;\r\n\r\n        let currentLength = 0;\r\n        let currentT = 0;\r\n        let prevPos = PathData.catmullRomPoint(0, p0, p1, p2, p3, smoothness);\r\n\r\n        const stepSize = 0.001; // 小步长用于精确计算\r\n\r\n        for (let segment = 1; segment <= segmentCount; segment++) {\r\n            const targetLength = segment * targetSegmentLength;\r\n\r\n            // 寻找对应的t值\r\n            while (currentLength < targetLength && currentT < 1) {\r\n                currentT += stepSize;\r\n                const currentPos = PathData.catmullRomPoint(currentT, p0, p1, p2, p3, smoothness);\r\n                currentLength += Vec2.distance(prevPos, currentPos);\r\n                prevPos = currentPos;\r\n            }\r\n\r\n            tValues.push(Math.min(currentT, 1));\r\n        }\r\n\r\n        return tValues;\r\n    }\r\n\r\n    /**\r\n     * 原有的自适应细分策略 - 作为备选方案\r\n     */\r\n    private generateAdaptiveSubdivisionPoints(effectivePoints: PathPoint[]): PathPoint[] {\r\n        const subdivided: PathPoint[] = [];\r\n        const pointCount = effectivePoints.length;\r\n\r\n        // 添加第一个点\r\n        subdivided.push(effectivePoints[0]);\r\n\r\n        // 计算需要处理的段数\r\n        const segmentCount = this.closed ? pointCount : pointCount - 1;\r\n\r\n        // 为每一段生成细分点\r\n        for (let i = 0; i < segmentCount; i++) {\r\n            const p0 = this.getControlPoint(effectivePoints, i - 1);\r\n            const p1 = effectivePoints[i].position;\r\n            const p2 = this.getControlPoint(effectivePoints, i + 1);\r\n            const p3 = this.getControlPoint(effectivePoints, i + 2);\r\n\r\n            const point = effectivePoints[i];\r\n            const pointNext = effectivePoints[(i + 1) % pointCount];\r\n\r\n            const startSmoothness = point.smoothness;\r\n            const endSmoothness = pointNext.smoothness;\r\n\r\n            // 如果任一端点的smoothness为0，则整段使用直线\r\n            if (startSmoothness === 0 || endSmoothness === 0) {\r\n                // 直线连接：只需要添加终点\r\n                subdivided.push(pointNext);\r\n            } else {\r\n                // 使用自适应细分算法\r\n                const segmentPoints = this.adaptiveSubdivision(p0, p1, p2, p3, point, pointNext);\r\n                subdivided.push(...segmentPoints);\r\n            }\r\n        }\r\n\r\n        // 处理闭合路径的重复点\r\n        if (this.closed && subdivided.length > 1) {\r\n            const firstPoint = subdivided[0];\r\n            const lastPoint = subdivided[subdivided.length - 1];\r\n            const distance = Vec2.distance(firstPoint.position, lastPoint.position);\r\n\r\n            if (distance < 0.1) {\r\n                subdivided.pop();\r\n            }\r\n        }\r\n\r\n        return subdivided;\r\n    }\r\n\r\n    /**\r\n     * 获取有效的路径点范围（考虑startIdx和endIdx）\r\n     */\r\n    private getPoints(): PathPoint[] {\r\n        if (this.points.length === 0) return [];\r\n\r\n        const startIndex = Math.max(0, Math.min(this.startIdx, this.points.length - 1));\r\n        const endIndex = this.endIdx === -1 ? this.points.length - 1 : Math.max(startIndex, Math.min(this.endIdx, this.points.length - 1));\r\n\r\n        return this.points.slice(startIndex, endIndex + 1);\r\n    }\r\n\r\n    /**\r\n     * 自适应细分算法 - 基于曲率和误差的智能细分\r\n     * @param p0 前一个控制点\r\n     * @param p1 起始点\r\n     * @param p2 结束点\r\n     * @param p3 后一个控制点\r\n     * @param point1 起始PathPoint\r\n     * @param point2 结束PathPoint\r\n     * @returns 细分后的PathPoint数组\r\n     */\r\n    private adaptiveSubdivision(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        maxDepth: number = 6\r\n    ): PathPoint[] {\r\n        const avgSmoothness = (point1.smoothness + point2.smoothness) / 2;\r\n\r\n        // 如果平滑度为0，直接返回终点\r\n        if (avgSmoothness === 0) {\r\n            return [point2];\r\n        }\r\n\r\n        // 递归细分（从深度0开始），但不包括t=1的终点\r\n        const subdivisionPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, 0, 0.999999, 0, maxDepth, avgSmoothness);\r\n\r\n        // 最后添加原始的终点，确保保留所有原始属性（包括stayDuration等）\r\n        subdivisionPoints.push(point2);\r\n\r\n        return subdivisionPoints;\r\n    }\r\n\r\n    /**\r\n     * 递归细分方法\r\n     */\r\n    static subdivideRecursive(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        t1: number, t2: number, depth: number, maxDepth: number, smoothness: number\r\n    ): PathPoint[] {\r\n        // 达到最大深度，停止细分\r\n        if (depth >= maxDepth) {\r\n            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];\r\n        }\r\n\r\n        const tMid = (t1 + t2) / 2;\r\n\r\n        // 计算三个点：起点、中点、终点\r\n        const startPos = PathData.catmullRomPoint(t1, p0, p1, p2, p3, smoothness);\r\n        const midPos = PathData.catmullRomPoint(tMid, p0, p1, p2, p3, smoothness);\r\n        const endPos = PathData.catmullRomPoint(t2, p0, p1, p2, p3, smoothness);\r\n\r\n        // 计算线性插值的中点\r\n        const linearMid = Vec2.lerp(new Vec2(), startPos, endPos, 0.5);\r\n\r\n        // 计算误差（曲线中点与线性中点的距离）\r\n        const error = Vec2.distance(midPos, linearMid);\r\n\r\n        // 计算曲率（使用三点法）\r\n        const curvature = PathData.calculateCurvature(startPos, midPos, endPos);\r\n\r\n        // 动态误差阈值：考虑距离和曲率\r\n        const distance = Vec2.distance(startPos, endPos);\r\n        const baseThreshold = Math.max(0.5, distance * 0.01); // 基础阈值\r\n        const curvatureThreshold = baseThreshold * (1 + curvature * 10); // 曲率调整\r\n        // console.log('error:', error, 'curvatureThreshold:', curvatureThreshold);\r\n        // 如果误差小于阈值，不需要进一步细分\r\n        if (error < curvatureThreshold) {\r\n            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];\r\n        }\r\n\r\n        // 需要细分：递归处理两个子段\r\n        const leftPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, tMid, depth + 1, maxDepth, smoothness);\r\n        const rightPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, tMid, t2, depth + 1, maxDepth, smoothness);\r\n\r\n        return [...leftPoints, ...rightPoints];\r\n    }\r\n\r\n    /**\r\n     * 计算三点的曲率\r\n     */\r\n    static calculateCurvature(p1: Vec2, p2: Vec2, p3: Vec2): number {\r\n        const v1 = Vec2.subtract(new Vec2(), p2, p1);\r\n        const v2 = Vec2.subtract(new Vec2(), p3, p2);\r\n\r\n        // 避免除零\r\n        const len1 = v1.length();\r\n        const len2 = v2.length();\r\n        if (len1 < 0.001 || len2 < 0.001) return 0;\r\n\r\n        v1.normalize();\r\n        v2.normalize();\r\n\r\n        // 计算角度变化\r\n        const dot = Vec2.dot(v1, v2);\r\n        const clampedDot = Math.max(-1, Math.min(1, dot));\r\n        const angle = Math.acos(clampedDot);\r\n\r\n        // 归一化曲率值\r\n        return angle / Math.PI;\r\n    }\r\n\r\n    /**\r\n     * 创建曲线插值的PathPoint（使用Catmull-Rom曲线）\r\n     */\r\n    static createCurveInterpolatedPoint(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        t: number, smoothness: number\r\n    ): PathPoint {\r\n        // 使用Catmull-Rom曲线计算位置\r\n        const pos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);\r\n        const newPoint = new PathPoint(pos.x, pos.y);\r\n\r\n        // 插值其他属性\r\n        newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;\r\n        newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;\r\n        newPoint.orientationType = point1.orientationType;\r\n        newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;\r\n        newPoint.isSubdivided = true;\r\n\r\n        return newPoint;\r\n    }\r\n\r\n    /**\r\n     * 获取有效路径点的控制点（处理边界情况）\r\n     */\r\n    private getControlPoint(effectivePoints: PathPoint[], index: number): Vec2 {\r\n        const pointCount = effectivePoints.length;\r\n\r\n        if (this.closed) {\r\n            // 闭合路径，使用循环索引\r\n            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n            return effectivePoints[wrappedIndex].position;\r\n        } else {\r\n            // 开放路径，边界处理\r\n            if (index < 0) {\r\n                // 延伸第一个点\r\n                const p0 = effectivePoints[0].position;\r\n                const p1 = effectivePoints[1].position;\r\n                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else if (index >= pointCount) {\r\n                // 延伸最后一个点\r\n                const p0 = effectivePoints[pointCount - 2].position;\r\n                const p1 = effectivePoints[pointCount - 1].position;\r\n                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else {\r\n                return effectivePoints[index].position;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取控制点（处理边界情况）- 保留用于兼容性\r\n     */\r\n    // private getControlPoint(index: number): Vec2 {\r\n    //     const pointCount = this.points.length;\r\n\r\n    //     if (this.closed) {\r\n    //         // 闭合路径，使用循环索引\r\n    //         const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n    //         return this.points[wrappedIndex].position;\r\n    //     } else {\r\n    //         // 开放路径，边界处理\r\n    //         if (index < 0) {\r\n    //             // 延伸第一个点\r\n    //             const p0 = this.points[0].position;\r\n    //             const p1 = this.points[1].position;\r\n    //             return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n    //         } else if (index >= pointCount) {\r\n    //             // 延伸最后一个点\r\n    //             const p0 = this.points[pointCount - 2].position;\r\n    //             const p1 = this.points[pointCount - 1].position;\r\n    //             return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n    //         } else {\r\n    //             return this.points[index].position;\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    /**\r\n     * 自定义序列化 - 排除缓存数据\r\n     */\r\n    public toJSON(): any {\r\n        return {\r\n            name: this.name,\r\n            startIdx: this.startIdx,\r\n            endIdx: this.endIdx,\r\n            points: this.points,\r\n            closed: this.closed\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 自定义反序列化 - 清除缓存确保重新计算\r\n     */\r\n    public fromJSON(data: any): void {\r\n        if (!data) return;\r\n\r\n        this.name = data.name || \"\";\r\n        this.startIdx = data.startIdx || 0;\r\n        this.endIdx = data.endIdx || -1;\r\n        this.points = data.points ? data.points.map((p: any) => {\r\n            const point = new PathPoint();\r\n            point.fromJSON(p);\r\n            return point;\r\n        }) : [];\r\n        this.closed = data.closed || false;\r\n\r\n        // 清除缓存，确保使用新数据重新计算\r\n        this._cachedSubdividedPoints = null;\r\n    }\r\n\r\n    /**\r\n     * 静态工厂方法 - 从JSON创建PathData实例\r\n     */\r\n    public static fromJSON(data: any): PathData {\r\n        const pathData = new PathData();\r\n        pathData.fromJSON(data);\r\n        return pathData;\r\n    }\r\n}"]}