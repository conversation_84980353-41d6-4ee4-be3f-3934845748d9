import { _decorator, Vec2, CCFloat, CCInteger, Enum } from 'cc';
import { eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable';
const { ccclass, property } = _decorator;

export enum eSamplingStrategy {
    UniformDistance,    // 均匀距离采样（推荐，减少抖动）
    AdaptiveSubdivision // 自适应细分（原有策略）
}

export enum ePathType {
    Custom,  // 自定义
    Circle,  // 圆形(圆形不需要让策划再去编辑点了，程序来生成)
}

/**
 * 路径点数据
 */
@ccclass("PathPoint")
export class PathPoint {
    @property({ type: CCFloat, displayName: "X坐标" })
    public x: number = 0;

    @property({ type: CCFloat, displayName: "Y坐标" })
    public y: number = 0;

    @property({ type: CCFloat, displayName: "平滑程度", range: [0, 1], slide: true, tooltip: "0=直线连接, 1=最大平滑曲线" })
    public smoothness: number = 1;

    @property({ type: CCInteger, displayName: "速度", tooltip: "飞机在此点的速度" })
    public speed: number = 500;

    @property({ type: CCInteger, displayName: "停留时间", tooltip: "飞机到达此点后停留时间（毫秒）" })
    public stayDuration: number = 0;

    @property({ type: Enum(eOrientationType), displayName: "朝向类型", tooltip: "飞机在此点的朝向" })
    public orientationType: eOrientationType = 0;

    @property({ type: CCInteger, displayName: "朝向参数", tooltip: "根据朝向类型不同而不同",
        visible() {
            // @ts-ignore
            return this.orientationType === eOrientationType.Fixed || this.orientationType === eOrientationType.Rotate;
        }
    })
    public orientationParam: number = 0;

    // 标记是否是插值的点（非原始点）
    private _isSubdivided: boolean = false;
    public get isSubdivided(): boolean {
        return this._isSubdivided;
    }
    public set isSubdivided(value: boolean) {
        this._isSubdivided = value;
    }

    constructor(x: number = 0, y: number = 0) {
        this.x = x;
        this.y = y;
    }

    public get position(): Vec2 {
        return new Vec2(this.x, this.y);
    }

    public set position(value: Vec2) {
        this.x = value.x;
        this.y = value.y;
    }

    public fromJSON(data: any): void {
        this.x = data.x || 0;
        this.y = data.y || 0;
        this.smoothness = data.smoothness || 1;
        this.speed = data.speed || 500;
        this.stayDuration = data.stayDuration || 0;
        this.orientationType = data.orientationType || 0;
        this.orientationParam = data.orientationParam || 0;
    }
}

/**
 * 路径数据
 */
@ccclass("PathData")
export class PathData {
    @property({ displayName: '路径名称', editorOnly: true })
    public name: string = "";

    @property({ type: Enum(ePathType), displayName: "路径类型"})
    public pathType: ePathType = ePathType.Custom;

    @property({ type: CCInteger, displayName: '起始点(默认0)', visible() {
        // @ts-ignore
        return this.pathType === ePathType.Custom;
    } })
    public startIdx: number = 0;

    @property({ type: CCInteger, displayName: '结束点(-1代表使用路径终点)', visible() {
        // @ts-ignore
        return this.pathType === ePathType.Custom;
    } })
    public endIdx: number = -1;

    @property({ type: [PathPoint], displayName: '路径点', visible() {
        // @ts-ignore
        return this.pathType === ePathType.Custom;
    } })
    public points: PathPoint[] = [];

    @property({ displayName: "是否闭合路径", tooltip: "路径是否形成闭环", visible() {
        // @ts-ignore
        return this.pathType === ePathType.Custom;
    } })
    public closed: boolean = false;

    @property({ type: Enum(eSamplingStrategy), displayName: "采样策略", tooltip: "路径点采样策略", visible() {
        // @ts-ignore
        return this.pathType === ePathType.Custom;
    } })
    public samplingStrategy: eSamplingStrategy = eSamplingStrategy.UniformDistance;

    // 缓存的路径数据（不参与序列化）
    private _cachedSubdividedPoints: PathPoint[] | null = null;

    /**
     * 获取Catmull-Rom曲线上的点
     * @param t 参数值 [0, 1]
     * @param p0 前一个控制点（用于计算切线）
     * @param p1 起始点（曲线经过此点）
     * @param p2 结束点（曲线经过此点）
     * @param p3 后一个控制点（用于计算切线）
     * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线
     */
    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {
        // 当smoothness为0时，直接返回线性插值（直线）
        if (smoothness === 0) {
            return Vec2.lerp(new Vec2(), p1, p2, t);
        }

        const t2 = t * t;
        const t3 = t2 * t;

        // 标准Catmull-Rom插值公式
        const catmullRom = new Vec2();
        catmullRom.x = 0.5 * (
            (2 * p1.x) +
            (-p0.x + p2.x) * t +
            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +
            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3
        );

        catmullRom.y = 0.5 * (
            (2 * p1.y) +
            (-p0.y + p2.y) * t +
            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +
            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3
        );

        // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合
        if (smoothness < 1) {
            const linear = Vec2.lerp(new Vec2(), p1, p2, t);
            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);
        }

        return catmullRom;
    }

    public getStartPoint(): PathPoint|null {
        if (this.startIdx < 0 || this.startIdx >= this.points.length) {
            return null;
        }
        return this.points[this.startIdx];
    }

    /**
     * 获取细分后的路径点（包含完整的PathPoint信息）
     * 这是推荐的新方法，替代generateCurvePoints + 重新采样的方式
     */
    public getSubdividedPoints(regen: boolean = false): PathPoint[] {
        if ((!this._cachedSubdividedPoints || this._cachedSubdividedPoints.length === 0) || regen) {
            this._cachedSubdividedPoints = this.generateSubdividedPointsInternal();
        }

        return this._cachedSubdividedPoints!;
    }

    /**
     * 内部方法：生成细分后的PathPoint数组
     */
    private generateSubdividedPointsInternal(): PathPoint[] {
        const effectivePoints = this.getPoints();

        if (effectivePoints.length < 2) {
            return effectivePoints;
        }

        // 选择采样策略
        switch (this.samplingStrategy) {
            case eSamplingStrategy.UniformDistance:
                return this.generateUniformDistancePoints(effectivePoints);
            case eSamplingStrategy.AdaptiveSubdivision:
                return this.generateAdaptiveSubdivisionPoints(effectivePoints);
            default:
                return this.generateUniformDistancePoints(effectivePoints);
        }
    }

    /**
     * 基于均匀距离的采样策略 - 推荐用于减少抖动
     */
    private generateUniformDistancePoints(effectivePoints: PathPoint[]): PathPoint[] {
        const subdivided: PathPoint[] = [];
        const pointCount = effectivePoints.length;
        const segmentCount = this.closed ? pointCount : pointCount - 1;

        // 添加第一个点
        subdivided.push(effectivePoints[0]);

        // 计算目标采样距离（基于速度和帧率）
        const targetDistance = this.calculateOptimalSamplingDistance(effectivePoints);

        for (let i = 0; i < segmentCount; i++) {
            const p0 = this.getControlPoint(effectivePoints, i - 1);
            const p1 = effectivePoints[i].position;
            const p2 = this.getControlPoint(effectivePoints, i + 1);
            const p3 = this.getControlPoint(effectivePoints, i + 2);

            const point = effectivePoints[i];
            const pointNext = effectivePoints[(i + 1) % pointCount];

            const startSmoothness = point.smoothness;
            const endSmoothness = pointNext.smoothness;

            if (startSmoothness === 0 || endSmoothness === 0) {
                // 直线连接：使用均匀距离采样
                const segmentPoints = this.sampleLineSegmentUniformly(point, pointNext, targetDistance);
                subdivided.push(...segmentPoints);
            } else {
                // 曲线：使用均匀距离采样
                const segmentPoints = this.sampleCurveSegmentUniformly(
                    p0, p1, p2, p3, point, pointNext, targetDistance
                );
                subdivided.push(...segmentPoints);
            }
        }

        // 处理闭合路径的重复点
        if (this.closed && subdivided.length > 1) {
            const firstPoint = subdivided[0];
            const lastPoint = subdivided[subdivided.length - 1];
            const distance = Vec2.distance(firstPoint.position, lastPoint.position);

            if (distance < 0.1) {
                subdivided.pop();
            }
        }

        // 后处理：平滑速度变化
        this.smoothSpeedTransitions(subdivided);

        return subdivided;
    }

    /**
     * 计算最优采样距离
     */
    private calculateOptimalSamplingDistance(effectivePoints: PathPoint[]): number {
        // 计算平均速度
        let totalSpeed = 0;
        for (const point of effectivePoints) {
            totalSpeed += point.speed;
        }
        const avgSpeed = totalSpeed / effectivePoints.length;

        // 基于速度和期望的时间间隔计算距离
        // 假设60FPS，每帧移动的距离应该让移动看起来平滑
        const targetFPS = 60;
        const targetTimeInterval = 1 / targetFPS; // 约16.67ms

        // 计算每帧期望移动的距离（像素）
        const baseDistance = (avgSpeed * targetTimeInterval) / 1000; // 速度单位转换

        // 限制在合理范围内：最小5像素，最大50像素
        return Math.max(5, Math.min(50, baseDistance));
    }

    /**
     * 均匀距离采样直线段
     */
    private sampleLineSegmentUniformly(point1: PathPoint, point2: PathPoint, targetDistance: number): PathPoint[] {
        const result: PathPoint[] = [];
        const startPos = point1.position;
        const endPos = point2.position;
        const totalDistance = Vec2.distance(startPos, endPos);

        if (totalDistance <= targetDistance) {
            // 距离太短，直接返回终点
            result.push(point2);
            return result;
        }

        const segmentCount = Math.ceil(totalDistance / targetDistance);

        for (let i = 1; i <= segmentCount; i++) {
            const t = i / segmentCount;
            const pos = Vec2.lerp(new Vec2(), startPos, endPos, t);

            if (i === segmentCount) {
                // 最后一个点使用原始终点，保持所有属性
                result.push(point2);
            } else {
                // 创建插值点
                const newPoint = new PathPoint(pos.x, pos.y);
                newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;
                newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;
                newPoint.orientationType = point1.orientationType;
                newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;
                newPoint.isSubdivided = true;
                result.push(newPoint);
            }
        }

        return result;
    }

    /**
     * 平滑速度变化 - 后处理步骤
     */
    private smoothSpeedTransitions(points: PathPoint[]): void {
        if (points.length < 3) return;

        // 使用简单的移动平均来平滑速度变化
        const smoothingWindow = 3;
        const originalSpeeds = points.map(p => p.speed);

        for (let i = 1; i < points.length - 1; i++) {
            let sum = 0;
            let count = 0;

            for (let j = Math.max(0, i - smoothingWindow); j <= Math.min(points.length - 1, i + smoothingWindow); j++) {
                sum += originalSpeeds[j];
                count++;
            }

            // 只对插值点进行平滑，保持原始点的速度
            if (points[i].isSubdivided) {
                points[i].speed = sum / count;
            }
        }
    }

    /**
     * 均匀距离采样曲线段
     */
    private sampleCurveSegmentUniformly(
        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,
        point1: PathPoint, point2: PathPoint,
        targetDistance: number
    ): PathPoint[] {
        const result: PathPoint[] = [];
        const avgSmoothness = (point1.smoothness + point2.smoothness) / 2;

        // 估算曲线长度（使用多个采样点）
        const estimatedLength = this.estimateCurveLength(p0, p1, p2, p3, avgSmoothness);

        if (estimatedLength <= targetDistance) {
            result.push(point2);
            return result;
        }

        const segmentCount = Math.ceil(estimatedLength / targetDistance);

        // 使用弧长参数化进行均匀采样
        const tValues = this.generateArcLengthParameterization(p0, p1, p2, p3, avgSmoothness, segmentCount);

        for (let i = 1; i <= segmentCount; i++) {
            const t = tValues[i];

            if (i === segmentCount) {
                // 最后一个点使用原始终点
                result.push(point2);
            } else {
                // 创建插值点
                const newPoint = PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t, avgSmoothness);
                result.push(newPoint);
            }
        }

        return result;
    }

    /**
     * 估算曲线长度
     */
    private estimateCurveLength(p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number): number {
        let length = 0;
        const samples = 20; // 使用20个采样点估算长度
        let prevPos = PathData.catmullRomPoint(0, p0, p1, p2, p3, smoothness);

        for (let i = 1; i <= samples; i++) {
            const t = i / samples;
            const currentPos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
            length += Vec2.distance(prevPos, currentPos);
            prevPos = currentPos;
        }

        return length;
    }

    /**
     * 生成弧长参数化的t值数组
     */
    private generateArcLengthParameterization(
        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,
        smoothness: number, segmentCount: number
    ): number[] {
        const tValues: number[] = [0]; // 起始点t=0
        const targetSegmentLength = this.estimateCurveLength(p0, p1, p2, p3, smoothness) / segmentCount;

        let currentLength = 0;
        let currentT = 0;
        let prevPos = PathData.catmullRomPoint(0, p0, p1, p2, p3, smoothness);

        const stepSize = 0.001; // 小步长用于精确计算

        for (let segment = 1; segment <= segmentCount; segment++) {
            const targetLength = segment * targetSegmentLength;

            // 寻找对应的t值
            while (currentLength < targetLength && currentT < 1) {
                currentT += stepSize;
                const currentPos = PathData.catmullRomPoint(currentT, p0, p1, p2, p3, smoothness);
                currentLength += Vec2.distance(prevPos, currentPos);
                prevPos = currentPos;
            }

            tValues.push(Math.min(currentT, 1));
        }

        return tValues;
    }

    /**
     * 原有的自适应细分策略 - 作为备选方案
     */
    private generateAdaptiveSubdivisionPoints(effectivePoints: PathPoint[]): PathPoint[] {
        const subdivided: PathPoint[] = [];
        const pointCount = effectivePoints.length;

        // 添加第一个点
        subdivided.push(effectivePoints[0]);

        // 计算需要处理的段数
        const segmentCount = this.closed ? pointCount : pointCount - 1;

        // 为每一段生成细分点
        for (let i = 0; i < segmentCount; i++) {
            const p0 = this.getControlPoint(effectivePoints, i - 1);
            const p1 = effectivePoints[i].position;
            const p2 = this.getControlPoint(effectivePoints, i + 1);
            const p3 = this.getControlPoint(effectivePoints, i + 2);

            const point = effectivePoints[i];
            const pointNext = effectivePoints[(i + 1) % pointCount];

            const startSmoothness = point.smoothness;
            const endSmoothness = pointNext.smoothness;

            // 如果任一端点的smoothness为0，则整段使用直线
            if (startSmoothness === 0 || endSmoothness === 0) {
                // 直线连接：只需要添加终点
                subdivided.push(pointNext);
            } else {
                // 使用自适应细分算法
                const segmentPoints = this.adaptiveSubdivision(p0, p1, p2, p3, point, pointNext);
                subdivided.push(...segmentPoints);
            }
        }

        // 处理闭合路径的重复点
        if (this.closed && subdivided.length > 1) {
            const firstPoint = subdivided[0];
            const lastPoint = subdivided[subdivided.length - 1];
            const distance = Vec2.distance(firstPoint.position, lastPoint.position);

            if (distance < 0.1) {
                subdivided.pop();
            }
        }

        return subdivided;
    }

    /**
     * 获取有效的路径点范围（考虑startIdx和endIdx）
     */
    private getPoints(): PathPoint[] {
        if (this.points.length === 0) return [];

        const startIndex = Math.max(0, Math.min(this.startIdx, this.points.length - 1));
        const endIndex = this.endIdx === -1 ? this.points.length - 1 : Math.max(startIndex, Math.min(this.endIdx, this.points.length - 1));

        return this.points.slice(startIndex, endIndex + 1);
    }

    /**
     * 自适应细分算法 - 基于曲率和误差的智能细分
     * @param p0 前一个控制点
     * @param p1 起始点
     * @param p2 结束点
     * @param p3 后一个控制点
     * @param point1 起始PathPoint
     * @param point2 结束PathPoint
     * @returns 细分后的PathPoint数组
     */
    private adaptiveSubdivision(
        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,
        point1: PathPoint, point2: PathPoint,
        maxDepth: number = 6
    ): PathPoint[] {
        const avgSmoothness = (point1.smoothness + point2.smoothness) / 2;

        // 如果平滑度为0，直接返回终点
        if (avgSmoothness === 0) {
            return [point2];
        }

        // 递归细分（从深度0开始），但不包括t=1的终点
        const subdivisionPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, 0, 0.999999, 0, maxDepth, avgSmoothness);

        // 最后添加原始的终点，确保保留所有原始属性（包括stayDuration等）
        subdivisionPoints.push(point2);

        return subdivisionPoints;
    }

    /**
     * 递归细分方法
     */
    static subdivideRecursive(
        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,
        point1: PathPoint, point2: PathPoint,
        t1: number, t2: number, depth: number, maxDepth: number, smoothness: number
    ): PathPoint[] {
        // 达到最大深度，停止细分
        if (depth >= maxDepth) {
            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];
        }

        const tMid = (t1 + t2) / 2;

        // 计算三个点：起点、中点、终点
        const startPos = PathData.catmullRomPoint(t1, p0, p1, p2, p3, smoothness);
        const midPos = PathData.catmullRomPoint(tMid, p0, p1, p2, p3, smoothness);
        const endPos = PathData.catmullRomPoint(t2, p0, p1, p2, p3, smoothness);

        // 计算线性插值的中点
        const linearMid = Vec2.lerp(new Vec2(), startPos, endPos, 0.5);

        // 计算误差（曲线中点与线性中点的距离）
        const error = Vec2.distance(midPos, linearMid);

        // 计算曲率（使用三点法）
        const curvature = PathData.calculateCurvature(startPos, midPos, endPos);

        // 动态误差阈值：考虑距离和曲率
        const distance = Vec2.distance(startPos, endPos);
        const baseThreshold = Math.max(0.5, distance * 0.01); // 基础阈值
        const curvatureThreshold = baseThreshold * (1 + curvature * 10); // 曲率调整
        // console.log('error:', error, 'curvatureThreshold:', curvatureThreshold);
        // 如果误差小于阈值，不需要进一步细分
        if (error < curvatureThreshold) {
            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];
        }

        // 需要细分：递归处理两个子段
        const leftPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, tMid, depth + 1, maxDepth, smoothness);
        const rightPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, tMid, t2, depth + 1, maxDepth, smoothness);

        return [...leftPoints, ...rightPoints];
    }

    /**
     * 计算三点的曲率
     */
    static calculateCurvature(p1: Vec2, p2: Vec2, p3: Vec2): number {
        const v1 = Vec2.subtract(new Vec2(), p2, p1);
        const v2 = Vec2.subtract(new Vec2(), p3, p2);

        // 避免除零
        const len1 = v1.length();
        const len2 = v2.length();
        if (len1 < 0.001 || len2 < 0.001) return 0;

        v1.normalize();
        v2.normalize();

        // 计算角度变化
        const dot = Vec2.dot(v1, v2);
        const clampedDot = Math.max(-1, Math.min(1, dot));
        const angle = Math.acos(clampedDot);

        // 归一化曲率值
        return angle / Math.PI;
    }

    /**
     * 创建曲线插值的PathPoint（使用Catmull-Rom曲线）
     */
    static createCurveInterpolatedPoint(
        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,
        point1: PathPoint, point2: PathPoint,
        t: number, smoothness: number
    ): PathPoint {
        // 使用Catmull-Rom曲线计算位置
        const pos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
        const newPoint = new PathPoint(pos.x, pos.y);

        // 插值其他属性
        newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;
        newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;
        newPoint.orientationType = point1.orientationType;
        newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;
        newPoint.isSubdivided = true;

        return newPoint;
    }

    /**
     * 获取有效路径点的控制点（处理边界情况）
     */
    private getControlPoint(effectivePoints: PathPoint[], index: number): Vec2 {
        const pointCount = effectivePoints.length;

        if (this.closed) {
            // 闭合路径，使用循环索引
            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;
            return effectivePoints[wrappedIndex].position;
        } else {
            // 开放路径，边界处理
            if (index < 0) {
                // 延伸第一个点
                const p0 = effectivePoints[0].position;
                const p1 = effectivePoints[1].position;
                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
                // 延伸最后一个点
                const p0 = effectivePoints[pointCount - 2].position;
                const p1 = effectivePoints[pointCount - 1].position;
                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
            } else {
                return effectivePoints[index].position;
            }
        }
    }

    /**
     * 获取控制点（处理边界情况）- 保留用于兼容性
     */
    // private getControlPoint(index: number): Vec2 {
    //     const pointCount = this.points.length;

    //     if (this.closed) {
    //         // 闭合路径，使用循环索引
    //         const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;
    //         return this.points[wrappedIndex].position;
    //     } else {
    //         // 开放路径，边界处理
    //         if (index < 0) {
    //             // 延伸第一个点
    //             const p0 = this.points[0].position;
    //             const p1 = this.points[1].position;
    //             return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
    //         } else if (index >= pointCount) {
    //             // 延伸最后一个点
    //             const p0 = this.points[pointCount - 2].position;
    //             const p1 = this.points[pointCount - 1].position;
    //             return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
    //         } else {
    //             return this.points[index].position;
    //         }
    //     }
    // }

    /**
     * 自定义序列化 - 排除缓存数据
     */
    public toJSON(): any {
        return {
            name: this.name,
            startIdx: this.startIdx,
            endIdx: this.endIdx,
            points: this.points,
            closed: this.closed
        };
    }

    /**
     * 自定义反序列化 - 清除缓存确保重新计算
     */
    public fromJSON(data: any): void {
        if (!data) return;

        this.name = data.name || "";
        this.startIdx = data.startIdx || 0;
        this.endIdx = data.endIdx || -1;
        this.points = data.points ? data.points.map((p: any) => {
            const point = new PathPoint();
            point.fromJSON(p);
            return point;
        }) : [];
        this.closed = data.closed || false;

        // 清除缓存，确保使用新数据重新计算
        this._cachedSubdividedPoints = null;
    }

    /**
     * 静态工厂方法 - 从JSON创建PathData实例
     */
    public static fromJSON(data: any): PathData {
        const pathData = new PathData();
        pathData.fromJSON(data);
        return pathData;
    }
}