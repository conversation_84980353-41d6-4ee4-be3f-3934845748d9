{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/PathMove.ts"], "names": ["_decorator", "misc", "Enum", "JsonAsset", "eMoveEvent", "eOrientationType", "PathData", "<PERSON><PERSON>ult<PERSON>ove", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "PathMove", "type", "displayName", "_pathAsset", "_pathData", "_subdivided", "_offsetX", "_offsetY", "_currentPointIndex", "_nextPointIndex", "_remainDistance", "_stayTimer", "_updateInEditor", "pathAsset", "value", "set<PERSON>ath", "fromJSON", "json", "editor_orientationType", "orientationType", "setOrientation", "editor_<PERSON><PERSON><PERSON><PERSON>", "orientationParam", "debugLog", "message", "DEBUG_ENABLED", "console", "log", "debugWarn", "warn", "setDebugEnabled", "enabled", "setDebugThresholds", "thresholds", "Object", "assign", "DEBUG_THRESHOLDS", "onFocusInEditor", "_isMovable", "onLostFocusInEditor", "resetToStart", "update", "dt", "tick", "setOffset", "x", "y", "pathData", "getSubdividedPoints", "frameTime", "toFixed", "moveToNextPoint", "tickMovement", "positionBeforeTilt", "_position", "updateTilting", "speedAngle", "tiltSpeed", "tiltOffset", "tiltDeltaX", "tiltDeltaY", "Math", "abs", "speed", "node", "setPosition", "checkVisibility", "updateOrientation", "v0", "s", "acceleration", "originalS", "originalDt", "newSpeed", "angleRad", "deltaX", "cos", "deltaY", "sin", "<PERSON><PERSON><PERSON><PERSON>", "sqrt", "singleFrameMove", "PI", "overshoot", "onReachPoint", "getDesiredOrientation", "Path", "targetAngleDegrees", "oldOrientation", "orientation", "newOrientation", "lerpAngle", "kLerpFactor", "orientationDiff", "orientationChange", "pointIndex", "currentPoint", "getPathPoint", "oldBasePos", "_basePosition", "targetX", "targetY", "positionJump", "pow", "positionAdjust", "oldSpeed", "speedChange", "currentPos", "getPosition", "targetPos", "initJump", "set", "nextPoint", "dirX", "dirY", "atan2", "stayDuration", "nextIndex", "length", "loop", "setNext", "emit", "onPathLoop", "onPathEnd", "pathPointIndex", "currentX", "currentY", "newAngle", "oldAngle", "angleDiff", "angleChange", "v1", "_tiltTime", "isStaying", "getRemainingStayTime", "from", "to", "decay", "normalizeAngle", "angle", "diff", "lerpedDiff", "exp"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAqCC,MAAAA,S,OAAAA,S;;AAExDC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,gB,iBAAAA,gB;;AAEtBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCR,I;OACzC;AAAES,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;0BAIpCa,Q,WAFZH,OAAO,CAAC,UAAD,C,UACPE,iBAAiB,E,UAGbD,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEX,SAAR;AAAmBY,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAWRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEZ,IAAI;AAAA;AAAA,iDAAZ;AAAgCa,QAAAA,WAAW,EAAC;AAA5C,OAAD,C,UAORJ,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE;AAAf,OAAD,C,qDA5Bb,MAEaF,QAFb;AAAA;AAAA,sCAE0C;AAAA;AAAA;AAAA,eAC/BG,UAD+B,GACA,IADA;;AAAA;;AAAA;;AAkCtC;AAlCsC,eAmC9BC,SAnC8B,GAmCD,IAnCC;AAAA,eAoC9BC,WApC8B,GAoCH,EApCG;AAoCC;AACvC;AArCsC,eAsC9BC,QAtC8B,GAsCX,CAtCW;AAAA,eAuC9BC,QAvC8B,GAuCX,CAvCW;AAgFtC;AAhFsC,eAiF9BC,kBAjF8B,GAiFD,CAjFC;AAiFE;AAjFF,eAkF9BC,eAlF8B,GAkFJ,CAlFI;AAAA,eAmF9BC,eAnF8B,GAmFJ,CAnFI;AAmFE;AAExC;AArFsC,eAsF9BC,UAtF8B,GAsFT,CAtFS;AAsFN;AAtFM,eAwF9BC,eAxF8B,GAwFH,KAxFG;AAAA;;AAGlB,YAATC,SAAS,GAAqB;AACrC,iBAAO,KAAKV,UAAZ;AACH;;AACmB,YAATU,SAAS,CAACC,KAAD,EAAmB;AACnC,eAAKX,UAAL,GAAkBW,KAAlB;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKC,OAAL,CAAa;AAAA;AAAA,sCAASC,QAAT,CAAkBF,KAAK,CAACG,IAAxB,CAAb;AACH;AACJ;;AASgC,YAAtBC,sBAAsB,GAAqB;AAClD,iBAAO,KAAKC,eAAZ;AACH;;AACgC,YAAtBD,sBAAsB,CAACJ,KAAD,EAA0B;AACvD,eAAKM,cAAL,CAAoBN,KAApB,EAA2B,KAAKO,uBAAhC;AACH;;AAEiC,YAAvBA,uBAAuB,GAAW;AACzC,iBAAO,KAAKC,gBAAZ;AACH;;AACiC,YAAvBD,uBAAuB,CAACP,KAAD,EAAgB;AAC9C,eAAKM,cAAL,CAAoB,KAAKD,eAAzB,EAA0CL,KAA1C;AACH;;AAyBsB,eAARS,QAAQ,CAACC,OAAD,EAAkB;AACrC,cAAIxB,QAAQ,CAACyB,aAAb,EAA4B;AACxBC,YAAAA,OAAO,CAACC,GAAR,CAAYH,OAAZ;AACH;AACJ;;AACuB,eAATI,SAAS,CAACJ,OAAD,EAAkB;AACtC,cAAIxB,QAAQ,CAACyB,aAAb,EAA4B;AACxBC,YAAAA,OAAO,CAACG,IAAR,CAAaL,OAAb;AACH;AACJ,SAlEqC,CAoEtC;;;AAC6B,eAAfM,eAAe,CAACC,OAAD,EAAmB;AAC5C/B,UAAAA,QAAQ,CAACyB,aAAT,GAAyBM,OAAzB;AACAL,UAAAA,OAAO,CAACC,GAAR,2CAA+BI,OAAO,GAAG,IAAH,GAAU,IAAhD;AACH,SAxEqC,CA0EtC;;;AACgC,eAAlBC,kBAAkB,CAACC,UAAD,EAAwD;AACpFC,UAAAA,MAAM,CAACC,MAAP,CAAcnC,QAAQ,CAACoC,gBAAvB,EAAyCH,UAAzC;AACAP,UAAAA,OAAO,CAACC,GAAR,2DAAmC3B,QAAQ,CAACoC,gBAA5C;AACH;;AAWMC,QAAAA,eAAe,GAAG;AACrB,eAAKzB,eAAL,GAAuB,IAAvB;AACA,eAAK0B,UAAL,GAAkB,IAAlB;AACH;;AACMC,QAAAA,mBAAmB,GAAG;AACzB,eAAK3B,eAAL,GAAuB,KAAvB;AACA,eAAK0B,UAAL,GAAkB,KAAlB;AACA,eAAKE,YAAL;AACH;;AACMC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,KAAK9B,eAAT,EAA0B;AACtB,iBAAK+B,IAAL,CAAUD,EAAV;AACH;AACJ,SAtGqC,CAwGtC;;;AACOE,QAAAA,SAAS,CAACC,CAAD,EAAYC,CAAZ,EAAiC;AAC7C,eAAKxC,QAAL,GAAgBuC,CAAhB;AACA,eAAKtC,QAAL,GAAgBuC,CAAhB;AACA,iBAAO,IAAP;AACH;;AACM/B,QAAAA,OAAO,CAACgC,QAAD,EAA+B;AACzC,eAAK3C,SAAL,GAAiB2C,QAAjB,CADyC,CAEzC;;AACA,eAAK1C,WAAL,GAAmB,KAAKD,SAAL,CAAe4C,mBAAf,EAAnB;AACA,eAAKR,YAAL;AAEA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWG,QAAAA,IAAI,CAACD,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKJ,UAAV,EAAsB;;AACtB,cAAI,CAAC,KAAKlC,SAAV,EAAqB;AACjB,kBAAMuC,IAAN,CAAWD,EAAX;AACA;AACH,WALyB,CAO1B;;;AACA,cAAIA,EAAE,GAAG1C,QAAQ,CAACoC,gBAAT,CAA0Ba,SAAnC,EAA8C;AAC1CjD,YAAAA,QAAQ,CAAC4B,SAAT,iDAAwC,CAACc,EAAE,GAAG,IAAN,EAAYQ,OAAZ,CAAoB,CAApB,CAAxC;AACH,WAVyB,CAY1B;;;AACA,cAAI,KAAKvC,UAAL,GAAkB,CAAtB,EAAyB;AACrB,iBAAKA,UAAL,IAAmB+B,EAAnB;;AACA,gBAAI,KAAK/B,UAAL,IAAmB,CAAvB,EAA0B;AACtB,mBAAKA,UAAL,GAAkB,CAAlB;AACAX,cAAAA,QAAQ,CAACuB,QAAT,oGAFsB,CAGtB;;AACA,mBAAK4B,eAAL;AACH;AACJ,WARD,MAQO,IAAI,KAAK1C,eAAL,KAAyB,KAAKD,kBAAlC,EAAsD;AACzD,iBAAK4C,YAAL,CAAkBV,EAAlB;AACH,WAvByB,CAyB1B;;;AACA,cAAMW,kBAAkB,GAAG;AAAER,YAAAA,CAAC,EAAE,KAAKS,SAAL,CAAeT,CAApB;AAAuBC,YAAAA,CAAC,EAAE,KAAKQ,SAAL,CAAeR;AAAzC,WAA3B;AACA,eAAKS,aAAL,CAAmB,KAAKC,UAAxB,EAAoCd,EAApC,EAAwC,KAAKY,SAA7C;;AACA,cAAI,KAAKG,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C,gBAAMC,UAAU,GAAG,KAAKL,SAAL,CAAeT,CAAf,GAAmBQ,kBAAkB,CAACR,CAAzD;AACA,gBAAMe,UAAU,GAAG,KAAKN,SAAL,CAAeR,CAAf,GAAmBO,kBAAkB,CAACP,CAAzD;;AACA,gBAAIe,IAAI,CAACC,GAAL,CAASH,UAAT,IAAuB3D,QAAQ,CAACoC,gBAAT,CAA0BsB,UAAjD,IAA+DG,IAAI,CAACC,GAAL,CAASF,UAAT,IAAuB5D,QAAQ,CAACoC,gBAAT,CAA0BsB,UAApH,EAAgI;AAC5H1D,cAAAA,QAAQ,CAACuB,QAAT,uCAA4CoC,UAAU,CAACT,OAAX,CAAmB,CAAnB,CAA5C,UAAsEU,UAAU,CAACV,OAAX,CAAmB,CAAnB,CAAtE,sBAA4G,KAAKO,SAAjH,sBAA2I,KAAKC,UAAhJ;AACH;AACJ;;AAED,cAAIG,IAAI,CAACC,GAAL,CAAS,KAAKC,KAAd,IAAuB,KAAvB,IAAgCF,IAAI,CAACC,GAAL,CAAS,KAAKL,SAAd,IAA2B,KAA/D,EAAsE;AAClE;AACA,iBAAKO,IAAL,CAAUC,WAAV,CAAsB,KAAKX,SAA3B;AACA,iBAAKY,eAAL,GAHkE,CAKlE;;AACA,iBAAKC,iBAAL,CAAuBzB,EAAvB;AACH;AACJ;;AAEOU,QAAAA,YAAY,CAACV,EAAD,EAAa;AAC7B;AACA,cAAM0B,EAAE,GAAG,KAAKL,KAAhB,CAF6B,CAG7B;;AACA,cAAIM,CAAC,GAAGD,EAAE,GAAG1B,EAAL,GAAU,MAAM,KAAK4B,YAAX,GAA0B5B,EAA1B,GAA+BA,EAAjD,CAJ6B,CAM7B;;AACA,cAAM6B,SAAS,GAAGF,CAAlB;AACA,cAAMG,UAAU,GAAG9B,EAAnB;;AAEA,cAAI2B,CAAC,GAAG,KAAK3D,eAAb,EAA8B;AAC1B2D,YAAAA,CAAC,GAAG,KAAK3D,eAAT,CAD0B,CAE1B;;AACA,gBAAI0D,EAAE,GAAG,KAAKE,YAAL,GAAoB5B,EAAzB,KAAgC,CAApC,EAAuC;AACnCA,cAAAA,EAAE,GAAG2B,CAAC,IAAID,EAAE,GAAG,MAAM,KAAKE,YAAX,GAA0B5B,EAAnC,CAAN;AACH;;AACD1C,YAAAA,QAAQ,CAACuB,QAAT,gFAA6CgD,SAAS,CAACrB,OAAV,CAAkB,CAAlB,CAA7C,iDAA+EmB,CAAC,CAACnB,OAAF,CAAU,CAAV,CAA/E,cAAoGsB,UAAU,CAACtB,OAAX,CAAmB,CAAnB,CAApG,YAAgIR,EAAE,CAACQ,OAAH,CAAW,CAAX,CAAhI;AACH;;AAED,cAAMuB,QAAQ,GAAG,KAAKV,KAAL,GAAa,KAAKO,YAAL,GAAoB5B,EAAlD,CAnB6B,CAqB7B;;AACA,cAAImB,IAAI,CAACC,GAAL,CAAS,KAAKQ,YAAd,IAA8B,GAAlC,EAAuC;AACnCtE,YAAAA,QAAQ,CAACuB,QAAT,8CAAyC6C,EAAE,CAAClB,OAAH,CAAW,CAAX,CAAzC,YAA6D,KAAKoB,YAAL,CAAkBpB,OAAlB,CAA0B,CAA1B,CAA7D,YAAgGmB,CAAC,CAACnB,OAAF,CAAU,CAAV,CAAhG,mBAA0HuB,QAAQ,CAACvB,OAAT,CAAiB,CAAjB,CAA1H,qBAA6J,KAAKxC,eAAL,CAAqBwC,OAArB,CAA6B,CAA7B,CAA7J;AACH;;AAED,eAAKa,KAAL,GAAaU,QAAb,CA1B6B,CA4B7B;;AACA,cAAMC,QAAQ,GAAG,KAAKlB,UAAtB;AACA,cAAMmB,MAAM,GAAGd,IAAI,CAACe,GAAL,CAASF,QAAT,IAAqBL,CAApC;AACA,cAAMQ,MAAM,GAAGhB,IAAI,CAACiB,GAAL,CAASJ,QAAT,IAAqBL,CAApC,CA/B6B,CAiC7B;AACA;AAEA;;AACA,eAAKf,SAAL,CAAeT,CAAf,IAAoB8B,MAApB;AACA,eAAKrB,SAAL,CAAeR,CAAf,IAAoB+B,MAApB,CAtC6B,CAwC7B;;AACA,cAAME,aAAa,GAAGlB,IAAI,CAACmB,IAAL,CAAUL,MAAM,GAAGA,MAAT,GAAkBE,MAAM,GAAGA,MAArC,CAAtB;;AACA,cAAIE,aAAa,GAAG/E,QAAQ,CAACoC,gBAAT,CAA0B6C,eAA9C,EAA+D;AAC3DjF,YAAAA,QAAQ,CAAC4B,SAAT,mEAA2CmD,aAAa,CAAC7B,OAAd,CAAsB,CAAtB,CAA3C,iBAA+EyB,MAAM,CAACzB,OAAP,CAAe,CAAf,CAA/E,UAAqG2B,MAAM,CAAC3B,OAAP,CAAe,CAAf,CAArG,wBAA+H,CAACwB,QAAQ,GAAG,GAAX,GAAiBb,IAAI,CAACqB,EAAvB,EAA2BhC,OAA3B,CAAmC,CAAnC,CAA/H;AACH,WA5C4B,CA8C7B;;;AACA,cAAI,KAAKxC,eAAL,GAAuB,CAA3B,EAA8B;AAC1B,iBAAKA,eAAL,IAAwB2D,CAAxB;;AACA,gBAAI,KAAK3D,eAAL,IAAwB,CAA5B,EAA+B;AAC3B;AACA;AACA,kBAAMyE,SAAS,GAAG,CAAC,KAAKzE,eAAxB;;AACA,kBAAIyE,SAAS,GAAG,GAAhB,EAAqB;AAAE;AACnBnF,gBAAAA,QAAQ,CAACuB,QAAT,gDAAsC4D,SAAS,CAACjC,OAAV,CAAkB,CAAlB,CAAtC,8DADiB,CAEjB;;AACA,oBAAMwB,SAAQ,GAAG,KAAKlB,UAAtB;AACA,qBAAKF,SAAL,CAAeT,CAAf,IAAoBgB,IAAI,CAACe,GAAL,CAASF,SAAT,IAAqBS,SAAzC;AACA,qBAAK7B,SAAL,CAAeR,CAAf,IAAoBe,IAAI,CAACiB,GAAL,CAASJ,SAAT,IAAqBS,SAAzC;AACH;;AAEDnF,cAAAA,QAAQ,CAACuB,QAAT,gDAAsC,KAAKd,eAA3C,oCAAqE,KAAKC,eAAL,CAAqBwC,OAArB,CAA6B,CAA7B,CAArE;AACA,mBAAKxC,eAAL,GAAuB,CAAvB,CAb2B,CAaD;;AAC1B,mBAAK0E,YAAL,CAAkB,KAAK3E,eAAvB;AACH;AACJ;AACJ;;AAES4E,QAAAA,qBAAqB,CAAC3C,EAAD,EAAqB;AAChD,cAAI,KAAKtC,SAAL,IAAkB,KAAKe,eAAL,KAAyB;AAAA;AAAA,oDAAiBmE,IAAhE,EAAsE;AAClE;AACA,gBAAMC,kBAAkB,GAAG3F,gBAAgB,CAAC,KAAK4D,UAAN,CAA3C;AACA,gBAAMgC,cAAc,GAAG,KAAKC,WAA5B;AACA,gBAAMC,cAAc,GAAG1F,QAAQ,CAAC2F,SAAT,CAAmB,KAAKF,WAAxB,EAAqCF,kBAArC,EAAyDvF,QAAQ,CAAC4F,WAAlE,EAA+ElD,EAA/E,CAAvB,CAJkE,CAMlE;;AACA,gBAAMmD,eAAe,GAAGhC,IAAI,CAACC,GAAL,CAAS4B,cAAc,GAAGF,cAA1B,CAAxB;;AACA,gBAAIK,eAAe,GAAG7F,QAAQ,CAACoC,gBAAT,CAA0B0D,iBAAhD,EAAmE;AAC/D9F,cAAAA,QAAQ,CAACuB,QAAT,2CAAsCiE,cAAc,CAACtC,OAAf,CAAuB,CAAvB,CAAtC,gBAAuEwC,cAAc,CAACxC,OAAf,CAAuB,CAAvB,CAAvE,4BAA0GqC,kBAAkB,CAACrC,OAAnB,CAA2B,CAA3B,CAA1G,mCAAmJ2C,eAAe,CAAC3C,OAAhB,CAAwB,CAAxB,CAAnJ;AACH;;AAED,mBAAOwC,cAAP;AACH;;AACD,iBAAO,MAAML,qBAAN,CAA4B3C,EAA5B,CAAP;AACH;;AAEO0C,QAAAA,YAAY,CAACW,UAAD,EAAqB;AACrC;AACA,eAAKvF,kBAAL,GAA0BuF,UAA1B,CAFqC,CAIrC;;AACA/F,UAAAA,QAAQ,CAACuB,QAAT,gDAAsCwE,UAAtC,EALqC,CAOrC;;AACA,cAAMC,YAAY,GAAG,KAAKC,YAAL,CAAkBF,UAAlB,CAArB;;AACA,cAAIC,YAAJ,EAAkB;AACd;AACA,gBAAME,UAAU,GAAG;AAAErD,cAAAA,CAAC,EAAE,KAAKsD,aAAL,CAAmBtD,CAAxB;AAA2BC,cAAAA,CAAC,EAAE,KAAKqD,aAAL,CAAmBrD;AAAjD,aAAnB;AACA,gBAAMsD,OAAO,GAAGJ,YAAY,CAACnD,CAAb,GAAiB,KAAKvC,QAAtC;AACA,gBAAM+F,OAAO,GAAGL,YAAY,CAAClD,CAAb,GAAiB,KAAKvC,QAAtC;AAEA,gBAAM+F,YAAY,GAAGzC,IAAI,CAACmB,IAAL,CACjBnB,IAAI,CAAC0C,GAAL,CAASH,OAAO,GAAGF,UAAU,CAACrD,CAA9B,EAAiC,CAAjC,IACAgB,IAAI,CAAC0C,GAAL,CAASF,OAAO,GAAGH,UAAU,CAACpD,CAA9B,EAAiC,CAAjC,CAFiB,CAArB,CANc,CAWd;;AACA,gBAAIwD,YAAY,GAAGtG,QAAQ,CAACoC,gBAAT,CAA0BkE,YAA7C,EAA2D;AACvDtG,cAAAA,QAAQ,CAAC4B,SAAT,6DAA0C0E,YAAY,CAACpD,OAAb,CAAqB,CAArB,CAA1C,kBAAyEgD,UAAU,CAACrD,CAAX,CAAaK,OAAb,CAAqB,CAArB,CAAzE,UAAqGgD,UAAU,CAACpD,CAAX,CAAaI,OAAb,CAAqB,CAArB,CAArG,kBAAoIkD,OAAO,CAAClD,OAAR,CAAgB,CAAhB,CAApI,UAA2JmD,OAAO,CAACnD,OAAR,CAAgB,CAAhB,CAA3J,8CADuD,CAGvD;AACA;;AACA,mBAAKiD,aAAL,CAAmBtD,CAAnB,GAAuB,KAAKS,SAAL,CAAeT,CAAtC;AACA,mBAAKsD,aAAL,CAAmBrD,CAAnB,GAAuB,KAAKQ,SAAL,CAAeR,CAAtC;AACH,aAPD,MAOO;AACH;AACA,mBAAKqD,aAAL,CAAmBtD,CAAnB,GAAuBuD,OAAvB;AACA,mBAAKD,aAAL,CAAmBrD,CAAnB,GAAuBuD,OAAvB;;AACA,kBAAIC,YAAY,GAAGtG,QAAQ,CAACoC,gBAAT,CAA0BoE,cAA7C,EAA6D;AACzDxG,gBAAAA,QAAQ,CAACuB,QAAT,2CAAsC+E,YAAY,CAACpD,OAAb,CAAqB,CAArB,CAAtC,kBAAqEgD,UAAU,CAACrD,CAAX,CAAaK,OAAb,CAAqB,CAArB,CAArE,UAAiGgD,UAAU,CAACpD,CAAX,CAAaI,OAAb,CAAqB,CAArB,CAAjG,kBAAgIkD,OAAO,CAAClD,OAAR,CAAgB,CAAhB,CAAhI,UAAuJmD,OAAO,CAACnD,OAAR,CAAgB,CAAhB,CAAvJ;AACH;AACJ,aA1Ba,CA4Bd;;;AACA,gBAAMuD,QAAQ,GAAG,KAAK1C,KAAtB;AACA,iBAAKA,KAAL,GAAaiC,YAAY,CAACjC,KAA1B;;AACA,gBAAIF,IAAI,CAACC,GAAL,CAAS,KAAKC,KAAL,GAAa0C,QAAtB,IAAkCzG,QAAQ,CAACoC,gBAAT,CAA0BsE,WAAhE,EAA6E;AACzE1G,cAAAA,QAAQ,CAACuB,QAAT,2CAAsCkF,QAAQ,CAACvD,OAAT,CAAiB,CAAjB,CAAtC,YAAgE,KAAKa,KAAL,CAAWb,OAAX,CAAmB,CAAnB,CAAhE;AACH,aAjCa,CAmCd;;;AACA,iBAAK/B,eAAL,GAAuB6E,YAAY,CAAC7E,eAApC;AACA,iBAAKG,gBAAL,GAAwB0E,YAAY,CAAC1E,gBAArC,CArCc,CAuCd;;AACA,gBAAIyE,UAAU,KAAK,CAAnB,EAAsB;AAClB;AACA,kBAAMY,UAAU,GAAG,KAAK3C,IAAL,CAAU4C,WAAV,EAAnB;AACA,kBAAMC,SAAS,GAAG,KAAKV,aAAvB;AACA,kBAAMW,QAAQ,GAAGjD,IAAI,CAACmB,IAAL,CACbnB,IAAI,CAAC0C,GAAL,CAASM,SAAS,CAAChE,CAAV,GAAc8D,UAAU,CAAC9D,CAAlC,EAAqC,CAArC,IACAgB,IAAI,CAAC0C,GAAL,CAASM,SAAS,CAAC/D,CAAV,GAAc6D,UAAU,CAAC7D,CAAlC,EAAqC,CAArC,CAFa,CAAjB;;AAKA,kBAAIgE,QAAQ,GAAG,CAAf,EAAkB;AACd9G,gBAAAA,QAAQ,CAACuB,QAAT,6DAAyCuF,QAAQ,CAAC5D,OAAT,CAAiB,CAAjB,CAAzC,kBAAoEyD,UAAU,CAAC9D,CAAX,CAAaK,OAAb,CAAqB,CAArB,CAApE,UAAgGyD,UAAU,CAAC7D,CAAX,CAAaI,OAAb,CAAqB,CAArB,CAAhG,kBAA+H2D,SAAS,CAAChE,CAAV,CAAYK,OAAZ,CAAoB,CAApB,CAA/H,UAA0J2D,SAAS,CAAC/D,CAAV,CAAYI,OAAZ,CAAoB,CAApB,CAA1J;AACH;;AAED,mBAAKI,SAAL,CAAeyD,GAAf,CAAmB,KAAKZ,aAAxB;;AACA,mBAAKnC,IAAL,CAAUC,WAAV,CAAsB,KAAKX,SAA3B;AACAtD,cAAAA,QAAQ,CAACuB,QAAT,kDAAwC,KAAK+B,SAAL,CAAeT,CAAf,CAAiBK,OAAjB,CAAyB,CAAzB,CAAxC,UAAwE,KAAKI,SAAL,CAAeR,CAAf,CAAiBI,OAAjB,CAAyB,CAAzB,CAAxE;AAEA,kBAAM8D,SAAS,GAAG,KAAKf,YAAL,CAAkBF,UAAU,GAAG,CAA/B,CAAlB;;AACA,kBAAIiB,SAAJ,EAAe;AACX,oBAAMC,IAAI,GAAGD,SAAS,CAACnE,CAAV,GAAcmD,YAAY,CAACnD,CAAxC;AACA,oBAAMqE,IAAI,GAAGF,SAAS,CAAClE,CAAV,GAAckD,YAAY,CAAClD,CAAxC;AACA,qBAAKU,UAAL,GAAkBK,IAAI,CAACsD,KAAL,CAAWD,IAAX,EAAiBD,IAAjB,CAAlB;AACAjH,gBAAAA,QAAQ,CAACuB,QAAT,iDAAuC,CAAC,KAAKiC,UAAL,GAAkB,GAAlB,GAAwBK,IAAI,CAACqB,EAA9B,EAAkChC,OAAlC,CAA0C,CAA1C,CAAvC;;AACA,oBAAI,KAAK/B,eAAL,KAAyB;AAAA;AAAA,0DAAiBmE,IAA9C,EAAoD;AAChD,uBAAKG,WAAL,GAAmB7F,gBAAgB,CAAC,KAAK4D,UAAN,CAAnC;AACH;AACJ;;AACD,mBAAKW,iBAAL,CAAuB,CAAvB;AACH;;AAEDnE,YAAAA,QAAQ,CAACuB,QAAT,oCAAoCwE,UAApC,oCAAyDC,YAAY,CAACoB,YAAtE;;AACA,gBAAIpB,YAAY,CAACoB,YAAb,GAA4B,CAAhC,EAAmC;AAC/B,mBAAKzG,UAAL,GAAkBqF,YAAY,CAACoB,YAAb,GAA4B,MAA9C;AACA;AACH;AACJ,WA3ED,MA2EO;AACHpH,YAAAA,QAAQ,CAAC4B,SAAT,4DAAyCmE,UAAzC;AACA,iBAAKhC,KAAL,GAAa,CAAb;AACH,WAvFoC,CAyFrC;;;AACA,eAAKZ,eAAL;AACH;;AAEOA,QAAAA,eAAe,GAAG;AACtB,cAAMkE,SAAS,GAAG,KAAK7G,kBAAL,GAA0B,CAA5C;;AACA,cAAI6G,SAAS,IAAI,KAAKhH,WAAL,CAAiBiH,MAAlC,EAA0C;AACtC;AACA,gBAAI,KAAKC,IAAT,EAAe;AACX;AACA,mBAAKC,OAAL,CAAa,CAAb;AACA,mBAAKC,IAAL,CAAU;AAAA;AAAA,4CAAWC,UAArB;AACH,aAJD,MAIO;AACH;AACA,mBAAKjH,eAAL,GAAuB,KAAKD,kBAA5B;AACA,mBAAKiH,IAAL,CAAU;AAAA;AAAA,4CAAWE,SAArB;AACH;AACJ,WAXD,MAWO;AACH;AACA,iBAAKH,OAAL,CAAaH,SAAb;AACH;AACJ;;AAEOG,QAAAA,OAAO,CAACI,cAAD,EAAyB;AACpC,eAAKnH,eAAL,GAAuBmH,cAAvB;AAEA,cAAM5B,YAAY,GAAG,KAAKC,YAAL,CAAkB,KAAKzF,kBAAvB,CAArB;AACA,cAAMwG,SAAS,GAAG,KAAKf,YAAL,CAAkB,KAAKxF,eAAvB,CAAlB;;AACA,cAAIuF,YAAY,IAAIgB,SAApB,EAA+B;AAC3B;AACA;AACA,gBAAMa,QAAQ,GAAG,KAAKvE,SAAL,CAAeT,CAAhC;AACA,gBAAMiF,QAAQ,GAAG,KAAKxE,SAAL,CAAeR,CAAhC;AACA,gBAAMsD,OAAO,GAAGY,SAAS,CAACnE,CAAV,GAAc,KAAKvC,QAAnC;AACA,gBAAM+F,OAAO,GAAGW,SAAS,CAAClE,CAAV,GAAc,KAAKvC,QAAnC;AAEA,gBAAM0G,IAAI,GAAGb,OAAO,GAAGyB,QAAvB;AACA,gBAAMX,IAAI,GAAGb,OAAO,GAAGyB,QAAvB;AACA,iBAAKpH,eAAL,GAAuBmD,IAAI,CAACmB,IAAL,CAAUiC,IAAI,GAAGA,IAAP,GAAcC,IAAI,GAAGA,IAA/B,CAAvB;;AAEA,gBAAI,KAAKxG,eAAL,GAAuB,CAA3B,EAA8B;AAC1B;AACA,kBAAMqH,QAAQ,GAAGlE,IAAI,CAACsD,KAAL,CAAWD,IAAX,EAAiBD,IAAjB,CAAjB;AACA,kBAAMe,QAAQ,GAAG,KAAKxE,UAAtB,CAH0B,CAK1B;;AACA,kBAAIyE,SAAS,GAAGF,QAAQ,GAAGC,QAA3B,CAN0B,CAO1B;;AACA,qBAAOC,SAAS,GAAGpE,IAAI,CAACqB,EAAxB,EAA4B+C,SAAS,IAAI,IAAIpE,IAAI,CAACqB,EAAtB;;AAC5B,qBAAO+C,SAAS,GAAG,CAACpE,IAAI,CAACqB,EAAzB,EAA6B+C,SAAS,IAAI,IAAIpE,IAAI,CAACqB,EAAtB;;AAE7B,kBAAIrB,IAAI,CAACC,GAAL,CAASmE,SAAT,IAAsBjI,QAAQ,CAACoC,gBAAT,CAA0B8F,WAApD,EAAiE;AAC7DlI,gBAAAA,QAAQ,CAAC4B,SAAT,uDAAyC,CAACqG,SAAS,GAAG,GAAZ,GAAkBpE,IAAI,CAACqB,EAAxB,EAA4BhC,OAA5B,CAAoC,CAApC,CAAzC,qBAAuF,CAAC8E,QAAQ,GAAG,GAAX,GAAiBnE,IAAI,CAACqB,EAAvB,EAA2BhC,OAA3B,CAAmC,CAAnC,CAAvF,oBAAmI,CAAC6E,QAAQ,GAAG,GAAX,GAAiBlE,IAAI,CAACqB,EAAvB,EAA2BhC,OAA3B,CAAmC,CAAnC,CAAnI;AACH;;AAED,mBAAKM,UAAL,GAAkBuE,QAAlB,CAf0B,CAiB1B;AACA;;AACA,kBAAM3D,EAAE,GAAG4B,YAAY,CAACjC,KAAxB;AACA,kBAAMoE,EAAE,GAAGnB,SAAS,CAACjD,KAArB;AACA,mBAAKO,YAAL,GAAoB,CAAC6D,EAAE,GAAGA,EAAL,GAAU/D,EAAE,GAAGA,EAAhB,KAAuB,IAAI,KAAK1D,eAAhC,CAApB,CArB0B,CAuB1B;;AACAV,cAAAA,QAAQ,CAACuB,QAAT,iDAAuC,KAAKf,kBAA5C,YAAqE,KAAKC,eAA1E,oCAAoG,KAAKC,eAAL,CAAqBwC,OAArB,CAA6B,CAA7B,CAApG,8BAA6IkB,EAA7I,8BAAyJ+D,EAAzJ,8BAAqK,KAAK7D,YAAL,CAAkBpB,OAAlB,CAA0B,CAA1B,CAArK,wBAA0M,CAAC6E,QAAQ,GAAG,GAAX,GAAiBlE,IAAI,CAACqB,EAAvB,EAA2BhC,OAA3B,CAAmC,CAAnC,CAA1M,WAxB0B,CA0B1B;;AACA,kBAAIW,IAAI,CAACC,GAAL,CAAS,KAAKQ,YAAd,IAA8BtE,QAAQ,CAACoC,gBAAT,CAA0BkC,YAA5D,EAA0E;AACtEtE,gBAAAA,QAAQ,CAAC4B,SAAT,iDAAwC,KAAK0C,YAAL,CAAkBpB,OAAlB,CAA0B,CAA1B,CAAxC;AACH;AACJ,aA9BD,MA8BO;AACHlD,cAAAA,QAAQ,CAACuB,QAAT,2CAAsC,KAAKb,eAAL,CAAqBwC,OAArB,CAA6B,CAA7B,CAAtC;AACH;AACJ;AACJ;;AAEO+C,QAAAA,YAAY,CAAC2B,cAAD,EAA2C;AAC3D,cAAIA,cAAc,GAAG,CAAjB,IAAsBA,cAAc,IAAI,KAAKvH,WAAL,CAAiBiH,MAA7D,EAAqE;AACjE,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKjH,WAAL,CAAiBuH,cAAjB,CAAP;AACH;;AAEOpF,QAAAA,YAAY,GAAG;AACnB,eAAKhC,kBAAL,GAA0B,CAA1B;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKE,UAAL,GAAkB,CAAlB;AACA,eAAKyH,SAAL,GAAiB,CAAjB;AACA,eAAK1H,eAAL,GAAuB,CAAvB;AACA,eAAKqD,KAAL,GAAa,CAAb;AACA,eAAKO,YAAL,GAAoB,CAApB;AACA,eAAKb,SAAL,GAAiB,CAAjB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AAEA,eAAK0B,YAAL,CAAkB,CAAlB;AACH;;AAEMiD,QAAAA,SAAS,GAAY;AACxB,iBAAO,KAAK1H,UAAL,GAAkB,CAAzB;AACH;;AAEM2H,QAAAA,oBAAoB,GAAW;AAClC,iBAAO,KAAK3H,UAAZ;AACH,SA9bqC,CAgctC;AACA;AACA;;AAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoB,eAATgF,SAAS,CAAC4C,IAAD,EAAeC,EAAf,EAA2BC,KAA3B,EAA0C/F,EAA1C,EAA8D;AAC1E;AACA,cAAMgG,cAAc,GAAIC,KAAD,IAA2B;AAC9C,mBAAOA,KAAK,GAAG,GAAf,EAAoBA,KAAK,IAAI,GAAT;;AACpB,mBAAOA,KAAK,GAAG,CAAC,GAAhB,EAAqBA,KAAK,IAAI,GAAT;;AACrB,mBAAOA,KAAP;AACH,WAJD,CAF0E,CAQ1E;;;AACAJ,UAAAA,IAAI,GAAGG,cAAc,CAACH,IAAD,CAArB;AACAC,UAAAA,EAAE,GAAGE,cAAc,CAACF,EAAD,CAAnB,CAV0E,CAY1E;;AACA,cAAII,IAAI,GAAGJ,EAAE,GAAGD,IAAhB;AACA,cAAIK,IAAI,GAAG,GAAX,EAAgBA,IAAI,IAAI,GAAR;AAChB,cAAIA,IAAI,GAAG,CAAC,GAAZ,EAAiBA,IAAI,IAAI,GAAR,CAfyD,CAiB1E;;AACA,cAAMC,UAAU,GAAGD,IAAI,IAAI,IAAI/E,IAAI,CAACiF,GAAL,CAAS,CAACL,KAAD,GAAS/F,EAAlB,CAAR,CAAvB;AACA,iBAAOgG,cAAc,CAACH,IAAI,GAAGM,UAAR,CAArB;AACH;;AAheqC,O,UAyCdjD,W,GAAc,E,UAGvBnE,a,GAAgB,I,UAChBW,gB,GAAmB;AAC9Ba,QAAAA,SAAS,EAAE,IADmB;AACN;AACxBqD,QAAAA,YAAY,EAAE,EAFgB;AAEN;AACxBE,QAAAA,cAAc,EAAE,CAHc;AAGN;AACxBvB,QAAAA,eAAe,EAAE,EAJa;AAIN;AACxBiD,QAAAA,WAAW,EAAErE,IAAI,CAACqB,EAAL,GAAU,CALO;AAKJ;AAC1BY,QAAAA,iBAAiB,EAAE,EANW;AAMN;AACxBxB,QAAAA,YAAY,EAAE,IAPgB;AAON;AACxBoC,QAAAA,WAAW,EAAE,EARiB;AAQN;AACxBhD,QAAAA,UAAU,EAAE,CATkB,CASN;;AATM,O;;;;;iBA/BX,K;;;;;;;iBAGG,K", "sourcesContent": ["import { _decorator, Component, misc, Enum, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';\nimport { BulletSystem } from '../bullet/BulletSystem';\nimport { MoveBase, eMoveEvent, eOrientationType } from './IMovable';\nimport Entity from '../ui/base/Entity';\nimport { PathData, PathPoint } from '../data/PathData';\nimport { DefaultMove } from './DefaultMove';\n\nconst { degreesToRadians, radiansToDegrees } = misc;\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('PathMove')\n@executeInEditMode()\nexport class PathMove extends DefaultMove {\n    public _pathAsset: JsonAsset | null = null;\n    @property({ type: JsonAsset, displayName: \"路径数据(预览用)\" })\n    public get pathAsset(): JsonAsset | null {\n        return this._pathAsset;\n    }\n    public set pathAsset(value: JsonAsset) {\n        this._pathAsset = value;\n        if (value) {\n            this.setPath(PathData.fromJSON(value.json));\n        }\n    }\n\n    @property({ displayName: \"循环移动\" })\n    public loop: boolean = false;\n\n    @property({ displayName: \"反向移动\" })\n    public reverse: boolean = false;\n\n    @property({ type: Enum(eOrientationType), displayName:\"朝向类型\" })\n    public get editor_orientationType(): eOrientationType {\n        return this.orientationType;\n    }\n    public set editor_orientationType(value: eOrientationType) {\n        this.setOrientation(value, this.editor_orientationParam);\n    }\n    @property({ displayName: \"朝向参数\" })\n    public get editor_orientationParam(): number {\n        return this.orientationParam;\n    }\n    public set editor_orientationParam(value: number) {\n        this.setOrientation(this.orientationType, value);\n    }\n\n    // 路径相关数据\n    private _pathData: PathData | null = null;\n    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）\n    // 路径偏移\n    private _offsetX: number = 0;\n    private _offsetY: number = 0;\n    // 平滑过渡参数（1-25)\n    private static readonly kLerpFactor = 16;\n\n    // [DEBUG] 调试开关和阈值\n    private static DEBUG_ENABLED = true;\n    private static DEBUG_THRESHOLDS = {\n        frameTime: 0.02,        // 帧时间阈值（秒）\n        positionJump: 10,       // 位置跳跃阈值（像素）\n        positionAdjust: 2,      // 位置微调阈值（像素）\n        singleFrameMove: 50,    // 单帧移动距离阈值（像素）\n        angleChange: Math.PI / 4, // 角度变化阈值（弧度，45度）\n        orientationChange: 10,  // 朝向变化阈值（度）\n        acceleration: 2000,     // 加速度阈值\n        speedChange: 50,        // 速度变化阈值\n        tiltOffset: 1           // tilting偏移阈值（像素）\n    };\n\n    private static debugLog(message: string) {\n        if (PathMove.DEBUG_ENABLED) {\n            console.log(message);\n        }\n    }\n    private static debugWarn(message: string) {\n        if (PathMove.DEBUG_ENABLED) {\n            console.warn(message);\n        }\n    }\n\n    // 公共方法：启用/禁用调试日志\n    public static setDebugEnabled(enabled: boolean) {\n        PathMove.DEBUG_ENABLED = enabled;\n        console.log(`[PathMove] 调试日志 ${enabled ? '启用' : '禁用'}`);\n    }\n\n    // 公共方法：设置调试阈值\n    public static setDebugThresholds(thresholds: Partial<typeof PathMove.DEBUG_THRESHOLDS>) {\n        Object.assign(PathMove.DEBUG_THRESHOLDS, thresholds);\n        console.log(`[PathMove] 调试阈值已更新:`, PathMove.DEBUG_THRESHOLDS);\n    }\n\n    // 移动状态\n    private _currentPointIndex: number = 0; // 当前所在的细分点索引\n    private _nextPointIndex: number = 0;\n    private _remainDistance: number = 0;    // 距离下一个点的剩余距离\n\n    // 停留状态\n    private _stayTimer: number = 0; // 停留计时器（秒）\n\n    private _updateInEditor: boolean = false;\n    public onFocusInEditor() {\n        this._updateInEditor = true;\n        this._isMovable = true;\n    }\n    public onLostFocusInEditor() {\n        this._updateInEditor = false;\n        this._isMovable = false;\n        this.resetToStart();\n    }\n    public update(dt: number) {\n        if (this._updateInEditor) {\n            this.tick(dt);\n        }\n    }\n\n    // 注意调用顺序,先调用setOffset,再调用setPath\n    public setOffset(x: number, y: number): PathMove {\n        this._offsetX = x;\n        this._offsetY = y;\n        return this;\n    }\n    public setPath(pathData: PathData): PathMove {\n        this._pathData = pathData;\n        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组\n        this._subdivided = this._pathData.getSubdividedPoints();\n        this.resetToStart();\n\n        return this;\n    }\n\n    /**\n     * 主要的移动更新逻辑\n     */\n    public tick(dt: number): void {\n        if (!this._isMovable) return;\n        if (!this._pathData) {\n            super.tick(dt);\n            return;\n        }\n\n        // [DEBUG] 记录基本状态\n        if (dt > PathMove.DEBUG_THRESHOLDS.frameTime) {\n            PathMove.debugWarn(`[PathMove] 帧时间过长: ${(dt * 1000).toFixed(2)}ms, 可能导致移动不平滑`);\n        }\n\n        // 处理停留逻辑\n        if (this._stayTimer > 0) {\n            this._stayTimer -= dt;\n            if (this._stayTimer <= 0) {\n                this._stayTimer = 0;\n                PathMove.debugLog(`[PathMove] 停留结束，继续移动到下一个点`);\n                // 停留结束，继续移动到下一个点\n                this.moveToNextPoint();\n            }\n        } else if (this._nextPointIndex !== this._currentPointIndex) {\n            this.tickMovement(dt);\n        }\n\n        // [DEBUG] 记录tilting状态\n        const positionBeforeTilt = { x: this._position.x, y: this._position.y };\n        this.updateTilting(this.speedAngle, dt, this._position);\n        if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\n            const tiltDeltaX = this._position.x - positionBeforeTilt.x;\n            const tiltDeltaY = this._position.y - positionBeforeTilt.y;\n            if (Math.abs(tiltDeltaX) > PathMove.DEBUG_THRESHOLDS.tiltOffset || Math.abs(tiltDeltaY) > PathMove.DEBUG_THRESHOLDS.tiltOffset) {\n                PathMove.debugLog(`[PathMove] Tilting偏移: (${tiltDeltaX.toFixed(2)}, ${tiltDeltaY.toFixed(2)}), tiltSpeed: ${this.tiltSpeed}, tiltOffset: ${this.tiltOffset}`);\n            }\n        }\n\n        if (Math.abs(this.speed) > 0.001 || Math.abs(this.tiltSpeed) > 0.001) {\n            // 设置节点位置\n            this.node.setPosition(this._position);\n            this.checkVisibility();\n\n            // 更新朝向\n            this.updateOrientation(dt);\n        }\n    }\n\n    private tickMovement(dt: number) {\n        // 使用匀加速直线运动更新位置\n        const v0 = this.speed;\n        // s = v0*t + 0.5*a*t^2\n        let s = v0 * dt + 0.5 * this.acceleration * dt * dt;\n\n        // [DEBUG] 记录运动计算\n        const originalS = s;\n        const originalDt = dt;\n\n        if (s > this._remainDistance) {\n            s = this._remainDistance;\n            // 重新计算实际需要的时间\n            if (v0 + this.acceleration * dt !== 0) {\n                dt = s / (v0 + 0.5 * this.acceleration * dt);\n            }\n            PathMove.debugLog(`[PathMove] 距离限制: 原始移动距离 ${originalS.toFixed(2)} -> 实际移动距离 ${s.toFixed(2)}, dt: ${originalDt.toFixed(4)} -> ${dt.toFixed(4)}`);\n        }\n\n        const newSpeed = this.speed + this.acceleration * dt;\n\n        // [DEBUG] 记录速度和加速度变化\n        if (Math.abs(this.acceleration) > 0.1) {\n            PathMove.debugLog(`[PathMove] 运动状态: v0=${v0.toFixed(2)}, a=${this.acceleration.toFixed(2)}, s=${s.toFixed(2)}, newSpeed=${newSpeed.toFixed(2)}, remainDist=${this._remainDistance.toFixed(2)}`);\n        }\n\n        this.speed = newSpeed;\n\n        // 计算移动向量\n        const angleRad = this.speedAngle;\n        const deltaX = Math.cos(angleRad) * s;\n        const deltaY = Math.sin(angleRad) * s;\n\n        // [DEBUG] 记录位置变化 (暂时注释掉未使用的变量)\n        // const oldPos = { x: this._position.x, y: this._position.y };\n\n        // 更新位置\n        this._position.x += deltaX;\n        this._position.y += deltaY;\n\n        // [DEBUG] 检查位置变化是否异常\n        const positionDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n        if (positionDelta > PathMove.DEBUG_THRESHOLDS.singleFrameMove) {\n            PathMove.debugWarn(`[PathMove] 单帧移动距离过大: ${positionDelta.toFixed(2)}, delta=(${deltaX.toFixed(2)}, ${deltaY.toFixed(2)}), 角度=${(angleRad * 180 / Math.PI).toFixed(2)}°`);\n        }\n\n        // 检查是否到达目标点\n        if (this._remainDistance > 0) {\n            this._remainDistance -= s;\n            if (this._remainDistance <= 0) {\n                // 当剩余距离为负数时，说明我们超过了目标点\n                // 需要回退到精确的目标点位置\n                const overshoot = -this._remainDistance;\n                if (overshoot > 0.1) { // 如果超调超过0.1像素\n                    PathMove.debugLog(`[PathMove] 超调目标点 ${overshoot.toFixed(2)} 像素，进行位置校正`);\n                    // 回退超调的距离\n                    const angleRad = this.speedAngle;\n                    this._position.x -= Math.cos(angleRad) * overshoot;\n                    this._position.y -= Math.sin(angleRad) * overshoot;\n                }\n\n                PathMove.debugLog(`[PathMove] 到达目标点 ${this._nextPointIndex}, 剩余距离: ${this._remainDistance.toFixed(2)}`);\n                this._remainDistance = 0; // 确保剩余距离为0\n                this.onReachPoint(this._nextPointIndex);\n            }\n        }\n    }\n\n    protected getDesiredOrientation(dt: number): number {\n        if (this._pathData && this.orientationType === eOrientationType.Path) {\n            // PathMove这里speedAngle是radians，需要转换为degrees进行角度lerp\n            const targetAngleDegrees = radiansToDegrees(this.speedAngle);\n            const oldOrientation = this.orientation;\n            const newOrientation = PathMove.lerpAngle(this.orientation, targetAngleDegrees, PathMove.kLerpFactor, dt);\n\n            // [DEBUG] 记录朝向变化\n            const orientationDiff = Math.abs(newOrientation - oldOrientation);\n            if (orientationDiff > PathMove.DEBUG_THRESHOLDS.orientationChange) {\n                PathMove.debugLog(`[PathMove] 朝向变化: ${oldOrientation.toFixed(2)}° -> ${newOrientation.toFixed(2)}° (目标: ${targetAngleDegrees.toFixed(2)}°), 变化量: ${orientationDiff.toFixed(2)}°`);\n            }\n\n            return newOrientation;\n        }\n        return super.getDesiredOrientation(dt);\n    }\n\n    private onReachPoint(pointIndex: number) {\n        // 更新当前点索引\n        this._currentPointIndex = pointIndex;\n\n        // [DEBUG] 记录到达点信息\n        PathMove.debugLog(`[PathMove] 到达路径点 ${pointIndex}`);\n\n        // 检查是否需要停留\n        const currentPoint = this.getPathPoint(pointIndex);\n        if (currentPoint) {\n            // [DEBUG] 记录位置更新\n            const oldBasePos = { x: this._basePosition.x, y: this._basePosition.y };\n            const targetX = currentPoint.x + this._offsetX;\n            const targetY = currentPoint.y + this._offsetY;\n\n            const positionJump = Math.sqrt(\n                Math.pow(targetX - oldBasePos.x, 2) +\n                Math.pow(targetY - oldBasePos.y, 2)\n            );\n\n            // 如果位置跳跃过大，使用平滑过渡而不是直接跳跃\n            if (positionJump > PathMove.DEBUG_THRESHOLDS.positionJump) {\n                PathMove.debugWarn(`[PathMove] 检测到位置跳跃: ${positionJump.toFixed(2)}, 从 (${oldBasePos.x.toFixed(2)}, ${oldBasePos.y.toFixed(2)}) 到 (${targetX.toFixed(2)}, ${targetY.toFixed(2)}), 使用平滑过渡`);\n\n                // 使用当前位置作为基础位置，避免突然跳跃\n                // 这样可以保持移动的连续性\n                this._basePosition.x = this._position.x;\n                this._basePosition.y = this._position.y;\n            } else {\n                // 位置跳跃较小，可以直接设置\n                this._basePosition.x = targetX;\n                this._basePosition.y = targetY;\n                if (positionJump > PathMove.DEBUG_THRESHOLDS.positionAdjust) {\n                    PathMove.debugLog(`[PathMove] 位置微调: ${positionJump.toFixed(2)}, 从 (${oldBasePos.x.toFixed(2)}, ${oldBasePos.y.toFixed(2)}) 到 (${targetX.toFixed(2)}, ${targetY.toFixed(2)})`);\n                }\n            }\n\n            // 设置速度\n            const oldSpeed = this.speed;\n            this.speed = currentPoint.speed;\n            if (Math.abs(this.speed - oldSpeed) > PathMove.DEBUG_THRESHOLDS.speedChange) {\n                PathMove.debugLog(`[PathMove] 速度变化: ${oldSpeed.toFixed(2)} -> ${this.speed.toFixed(2)}`);\n            }\n\n            // 设置朝向\n            this.orientationType = currentPoint.orientationType;\n            this.orientationParam = currentPoint.orientationParam;\n\n            // 这里要考虑的问题是: 第一个点的初始朝向,希望是立刻生效,而不是lerp\n            if (pointIndex === 0) {\n                // 对于第一个点，检查是否需要位置跳跃\n                const currentPos = this.node.getPosition();\n                const targetPos = this._basePosition;\n                const initJump = Math.sqrt(\n                    Math.pow(targetPos.x - currentPos.x, 2) +\n                    Math.pow(targetPos.y - currentPos.y, 2)\n                );\n\n                if (initJump > 5) {\n                    PathMove.debugLog(`[PathMove] 初始化位置跳跃: ${initJump.toFixed(2)}, 从 (${currentPos.x.toFixed(2)}, ${currentPos.y.toFixed(2)}) 到 (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);\n                }\n\n                this._position.set(this._basePosition);\n                this.node.setPosition(this._position);\n                PathMove.debugLog(`[PathMove] 初始化位置: (${this._position.x.toFixed(2)}, ${this._position.y.toFixed(2)})`);\n\n                const nextPoint = this.getPathPoint(pointIndex + 1);\n                if (nextPoint) {\n                    const dirX = nextPoint.x - currentPoint.x;\n                    const dirY = nextPoint.y - currentPoint.y;\n                    this.speedAngle = Math.atan2(dirY, dirX);\n                    PathMove.debugLog(`[PathMove] 初始化角度: ${(this.speedAngle * 180 / Math.PI).toFixed(2)}°`);\n                    if (this.orientationType === eOrientationType.Path) {\n                        this.orientation = radiansToDegrees(this.speedAngle);\n                    }\n                }\n                this.updateOrientation(0);\n            }\n\n            PathMove.debugLog(`[PathMove] 到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);\n            if (currentPoint.stayDuration > 0) {\n                this._stayTimer = currentPoint.stayDuration / 1000.0;\n                return;\n            }\n        } else {\n            PathMove.debugWarn(`[PathMove] 无法获取路径点 ${pointIndex}, 停止移动`);\n            this.speed = 0;\n        }\n\n        // 继续移动到下一个点\n        this.moveToNextPoint();\n    }\n\n    private moveToNextPoint() {\n        const nextIndex = this._currentPointIndex + 1;\n        if (nextIndex >= this._subdivided.length) {\n            // 到达路径终点\n            if (this.loop) {\n                // 循环模式，回到起点\n                this.setNext(0);\n                this.emit(eMoveEvent.onPathLoop);\n            } else {\n                // 停止移动\n                this._nextPointIndex = this._currentPointIndex;\n                this.emit(eMoveEvent.onPathEnd);\n            }\n        } else {\n            // 移动到下一个点\n            this.setNext(nextIndex);\n        }\n    }\n\n    private setNext(pathPointIndex: number) {\n        this._nextPointIndex = pathPointIndex;\n\n        const currentPoint = this.getPathPoint(this._currentPointIndex);\n        const nextPoint = this.getPathPoint(this._nextPointIndex);\n        if (currentPoint && nextPoint) {\n            // 使用当前实际位置而不是路径点位置来计算距离\n            // 这样可以减少累积误差\n            const currentX = this._position.x;\n            const currentY = this._position.y;\n            const targetX = nextPoint.x + this._offsetX;\n            const targetY = nextPoint.y + this._offsetY;\n\n            const dirX = targetX - currentX;\n            const dirY = targetY - currentY;\n            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);\n\n            if (this._remainDistance > 1) {\n                // 计算目标移动角度\n                const newAngle = Math.atan2(dirY, dirX);\n                const oldAngle = this.speedAngle;\n\n                // [DEBUG] 检查角度变化是否过大\n                let angleDiff = newAngle - oldAngle;\n                // 标准化角度差到 [-π, π] 范围\n                while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;\n                while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;\n\n                if (Math.abs(angleDiff) > PathMove.DEBUG_THRESHOLDS.angleChange) {\n                    PathMove.debugWarn(`[PathMove] 角度变化过大: ${(angleDiff * 180 / Math.PI).toFixed(2)}°, 从 ${(oldAngle * 180 / Math.PI).toFixed(2)}° 到 ${(newAngle * 180 / Math.PI).toFixed(2)}°`);\n                }\n\n                this.speedAngle = newAngle;\n\n                // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x\n                // 解出 a = (v1^2 - v0^2) / (2*x)\n                const v0 = currentPoint.speed;\n                const v1 = nextPoint.speed;\n                this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance);\n\n                // [DEBUG] 记录路径段设置\n                PathMove.debugLog(`[PathMove] 设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 实际距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${(newAngle * 180 / Math.PI).toFixed(2)}°`);\n\n                // [DEBUG] 检查加速度是否异常\n                if (Math.abs(this.acceleration) > PathMove.DEBUG_THRESHOLDS.acceleration) {\n                    PathMove.debugWarn(`[PathMove] 加速度过大: ${this.acceleration.toFixed(2)}, 可能导致移动不平滑`);\n                }\n            } else {\n                PathMove.debugLog(`[PathMove] 距离过小 (${this._remainDistance.toFixed(2)}), 跳过此段`);\n            }\n        }\n    }\n\n    private getPathPoint(pathPointIndex: number): PathPoint | null {\n        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {\n            return null;\n        }\n        return this._subdivided[pathPointIndex];\n    }\n\n    private resetToStart() {\n        this._currentPointIndex = 0;\n        this._nextPointIndex = 0;\n        this._stayTimer = 0;\n        this._tiltTime = 0;\n        this._remainDistance = 0;\n        this.speed = 0;\n        this.acceleration = 0;\n        this.tiltSpeed = 0;\n        this.tiltOffset = 0;\n\n        this.onReachPoint(0);\n    }\n\n    public isStaying(): boolean {\n        return this._stayTimer > 0;\n    }\n\n    public getRemainingStayTime(): number {\n        return this._stayTimer;\n    }\n\n    // static lerp(a: number, b: number, decay: number, dt: number): number {\n    //     return (a - b) * Math.exp(-decay * dt) + b;\n    // }\n\n    /**\n     * 角度插值，正确处理角度环绕问题\n     * @param from 起始角度（度）\n     * @param to 目标角度（度）\n     * @param decay 衰减系数\n     * @param dt 时间增量\n     * @returns 插值后的角度（度）\n     */\n    static lerpAngle(from: number, to: number, decay: number, dt: number): number {\n        // 将角度标准化到[-180, 180]范围\n        const normalizeAngle = (angle: number): number => {\n            while (angle > 180) angle -= 360;\n            while (angle < -180) angle += 360;\n            return angle;\n        };\n\n        // 标准化输入角度\n        from = normalizeAngle(from);\n        to = normalizeAngle(to);\n\n        // 计算角度差，选择最短路径\n        let diff = to - from;\n        if (diff > 180) diff -= 360;\n        if (diff < -180) diff += 360;\n\n        // 使用指数衰减插值\n        const lerpedDiff = diff * (1 - Math.exp(-decay * dt));\n        return normalizeAngle(from + lerpedDiff);\n    }\n}\n"]}