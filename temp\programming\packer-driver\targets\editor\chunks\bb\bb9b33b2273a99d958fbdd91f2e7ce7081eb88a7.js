System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, CC<PERSON>loat, CC<PERSON>nteger, Enum, eOrientationType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _class4, _class5, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _crd, ccclass, property, eSamplingStrategy, ePathType, PathPoint, PathData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      eOrientationType = _unresolved_2.eOrientationType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "762b8DNnsxGiYntrIbD801l", "PathData", undefined);

      __checkObsolete__(['_decorator', 'Vec2', 'CCFloat', 'CCInteger', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eSamplingStrategy", eSamplingStrategy = /*#__PURE__*/function (eSamplingStrategy) {
        eSamplingStrategy[eSamplingStrategy["UniformDistance"] = 0] = "UniformDistance";
        eSamplingStrategy[eSamplingStrategy["AdaptiveSubdivision"] = 1] = "AdaptiveSubdivision";
        return eSamplingStrategy;
      }({}));

      _export("ePathType", ePathType = /*#__PURE__*/function (ePathType) {
        ePathType[ePathType["Custom"] = 0] = "Custom";
        ePathType[ePathType["Circle"] = 1] = "Circle";
        return ePathType;
      }({}));
      /**
       * 路径点数据
       */


      _export("PathPoint", PathPoint = (_dec = ccclass("PathPoint"), _dec2 = property({
        type: CCFloat,
        displayName: "X坐标"
      }), _dec3 = property({
        type: CCFloat,
        displayName: "Y坐标"
      }), _dec4 = property({
        type: CCFloat,
        displayName: "平滑程度",
        range: [0, 1],
        slide: true,
        tooltip: "0=直线连接, 1=最大平滑曲线"
      }), _dec5 = property({
        type: CCInteger,
        displayName: "速度",
        tooltip: "飞机在此点的速度"
      }), _dec6 = property({
        type: CCInteger,
        displayName: "停留时间",
        tooltip: "飞机到达此点后停留时间（毫秒）"
      }), _dec7 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型",
        tooltip: "飞机在此点的朝向"
      }), _dec8 = property({
        type: CCInteger,
        displayName: "朝向参数",
        tooltip: "根据朝向类型不同而不同",

        visible() {
          // @ts-ignore
          return this.orientationType === (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
            error: Error()
          }), eOrientationType) : eOrientationType).Fixed || this.orientationType === (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
            error: Error()
          }), eOrientationType) : eOrientationType).Rotate;
        }

      }), _dec(_class = (_class2 = class PathPoint {
        get isSubdivided() {
          return this._isSubdivided;
        }

        set isSubdivided(value) {
          this._isSubdivided = value;
        }

        constructor(x = 0, y = 0) {
          _initializerDefineProperty(this, "x", _descriptor, this);

          _initializerDefineProperty(this, "y", _descriptor2, this);

          _initializerDefineProperty(this, "smoothness", _descriptor3, this);

          _initializerDefineProperty(this, "speed", _descriptor4, this);

          _initializerDefineProperty(this, "stayDuration", _descriptor5, this);

          _initializerDefineProperty(this, "orientationType", _descriptor6, this);

          _initializerDefineProperty(this, "orientationParam", _descriptor7, this);

          // 标记是否是插值的点（非原始点）
          this._isSubdivided = false;
          this.x = x;
          this.y = y;
        }

        get position() {
          return new Vec2(this.x, this.y);
        }

        set position(value) {
          this.x = value.x;
          this.y = value.y;
        }

        fromJSON(data) {
          this.x = data.x || 0;
          this.y = data.y || 0;
          this.smoothness = data.smoothness || 1;
          this.speed = data.speed || 500;
          this.stayDuration = data.stayDuration || 0;
          this.orientationType = data.orientationType || 0;
          this.orientationParam = data.orientationParam || 0;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "x", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "y", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "smoothness", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 500;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "stayDuration", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "orientationType", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "orientationParam", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));
      /**
       * 路径数据
       */


      _export("PathData", PathData = (_dec9 = ccclass("PathData"), _dec10 = property({
        displayName: '路径名称',
        editorOnly: true
      }), _dec11 = property({
        type: Enum(ePathType),
        displayName: "路径类型"
      }), _dec12 = property({
        type: CCInteger,
        displayName: '起始点(默认0)',

        visible() {
          // @ts-ignore
          return this.pathType === ePathType.Custom;
        }

      }), _dec13 = property({
        type: CCInteger,
        displayName: '结束点(-1代表使用路径终点)',

        visible() {
          // @ts-ignore
          return this.pathType === ePathType.Custom;
        }

      }), _dec14 = property({
        type: [PathPoint],
        displayName: '路径点',

        visible() {
          // @ts-ignore
          return this.pathType === ePathType.Custom;
        }

      }), _dec15 = property({
        displayName: "是否闭合路径",
        tooltip: "路径是否形成闭环",

        visible() {
          // @ts-ignore
          return this.pathType === ePathType.Custom;
        }

      }), _dec16 = property({
        type: Enum(eSamplingStrategy),
        displayName: "采样策略",
        tooltip: "路径点采样策略",

        visible() {
          // @ts-ignore
          return this.pathType === ePathType.Custom;
        }

      }), _dec9(_class4 = (_class5 = class PathData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor8, this);

          _initializerDefineProperty(this, "pathType", _descriptor9, this);

          _initializerDefineProperty(this, "startIdx", _descriptor10, this);

          _initializerDefineProperty(this, "endIdx", _descriptor11, this);

          _initializerDefineProperty(this, "points", _descriptor12, this);

          _initializerDefineProperty(this, "closed", _descriptor13, this);

          _initializerDefineProperty(this, "samplingStrategy", _descriptor14, this);

          // 缓存的路径数据（不参与序列化）
          this._cachedSubdividedPoints = null;
        }

        /**
         * 获取Catmull-Rom曲线上的点
         * @param t 参数值 [0, 1]
         * @param p0 前一个控制点（用于计算切线）
         * @param p1 起始点（曲线经过此点）
         * @param p2 结束点（曲线经过此点）
         * @param p3 后一个控制点（用于计算切线）
         * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线
         */
        static catmullRomPoint(t, p0, p1, p2, p3, smoothness = 0.5) {
          // 当smoothness为0时，直接返回线性插值（直线）
          if (smoothness === 0) {
            return Vec2.lerp(new Vec2(), p1, p2, t);
          }

          const t2 = t * t;
          const t3 = t2 * t; // 标准Catmull-Rom插值公式

          const catmullRom = new Vec2();
          catmullRom.x = 0.5 * (2 * p1.x + (-p0.x + p2.x) * t + (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 + (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3);
          catmullRom.y = 0.5 * (2 * p1.y + (-p0.y + p2.y) * t + (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 + (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3); // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合

          if (smoothness < 1) {
            const linear = Vec2.lerp(new Vec2(), p1, p2, t);
            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);
          }

          return catmullRom;
        }

        getStartPoint() {
          if (this.startIdx < 0 || this.startIdx >= this.points.length) {
            return null;
          }

          return this.points[this.startIdx];
        }
        /**
         * 获取细分后的路径点（包含完整的PathPoint信息）
         * 这是推荐的新方法，替代generateCurvePoints + 重新采样的方式
         */


        getSubdividedPoints(regen = false) {
          if (!this._cachedSubdividedPoints || this._cachedSubdividedPoints.length === 0 || regen) {
            this._cachedSubdividedPoints = this.generateSubdividedPointsInternal();
          }

          return this._cachedSubdividedPoints;
        }
        /**
         * 内部方法：生成细分后的PathPoint数组
         */


        generateSubdividedPointsInternal() {
          const effectivePoints = this.getPoints();

          if (effectivePoints.length < 2) {
            return effectivePoints;
          } // 选择采样策略


          switch (this.samplingStrategy) {
            case eSamplingStrategy.UniformDistance:
              return this.generateUniformDistancePoints(effectivePoints);

            case eSamplingStrategy.AdaptiveSubdivision:
              return this.generateAdaptiveSubdivisionPoints(effectivePoints);

            default:
              return this.generateUniformDistancePoints(effectivePoints);
          }
        }
        /**
         * 基于均匀距离的采样策略 - 推荐用于减少抖动
         */


        generateUniformDistancePoints(effectivePoints) {
          const subdivided = [];
          const pointCount = effectivePoints.length;
          const segmentCount = this.closed ? pointCount : pointCount - 1; // 添加第一个点

          subdivided.push(effectivePoints[0]); // 计算目标采样距离（基于速度和帧率）

          const targetDistance = this.calculateOptimalSamplingDistance(effectivePoints);

          for (let i = 0; i < segmentCount; i++) {
            const p0 = this.getControlPoint(effectivePoints, i - 1);
            const p1 = effectivePoints[i].position;
            const p2 = this.getControlPoint(effectivePoints, i + 1);
            const p3 = this.getControlPoint(effectivePoints, i + 2);
            const point = effectivePoints[i];
            const pointNext = effectivePoints[(i + 1) % pointCount];
            const startSmoothness = point.smoothness;
            const endSmoothness = pointNext.smoothness;

            if (startSmoothness === 0 || endSmoothness === 0) {
              // 直线连接：使用均匀距离采样
              const segmentPoints = this.sampleLineSegmentUniformly(point, pointNext, targetDistance);
              subdivided.push(...segmentPoints);
            } else {
              // 曲线：使用均匀距离采样
              const segmentPoints = this.sampleCurveSegmentUniformly(p0, p1, p2, p3, point, pointNext, targetDistance);
              subdivided.push(...segmentPoints);
            }
          } // 处理闭合路径的重复点


          if (this.closed && subdivided.length > 1) {
            const firstPoint = subdivided[0];
            const lastPoint = subdivided[subdivided.length - 1];
            const distance = Vec2.distance(firstPoint.position, lastPoint.position);

            if (distance < 0.1) {
              subdivided.pop();
            }
          } // 后处理：平滑速度变化


          this.smoothSpeedTransitions(subdivided);
          return subdivided;
        }
        /**
         * 计算最优采样距离
         */


        calculateOptimalSamplingDistance(effectivePoints) {
          // 计算平均速度
          let totalSpeed = 0;

          for (const point of effectivePoints) {
            totalSpeed += point.speed;
          }

          const avgSpeed = totalSpeed / effectivePoints.length; // 基于速度和期望的时间间隔计算距离
          // 假设60FPS，每帧移动的距离应该让移动看起来平滑

          const targetFPS = 60;
          const targetTimeInterval = 1 / targetFPS; // 约16.67ms
          // 计算每帧期望移动的距离（像素）

          const baseDistance = avgSpeed * targetTimeInterval / 1000; // 速度单位转换
          // 限制在合理范围内：最小5像素，最大50像素

          return Math.max(5, Math.min(50, baseDistance));
        }
        /**
         * 均匀距离采样直线段
         */


        sampleLineSegmentUniformly(point1, point2, targetDistance) {
          const result = [];
          const startPos = point1.position;
          const endPos = point2.position;
          const totalDistance = Vec2.distance(startPos, endPos);

          if (totalDistance <= targetDistance) {
            // 距离太短，直接返回终点
            result.push(point2);
            return result;
          }

          const segmentCount = Math.ceil(totalDistance / targetDistance);

          for (let i = 1; i <= segmentCount; i++) {
            const t = i / segmentCount;
            const pos = Vec2.lerp(new Vec2(), startPos, endPos, t);

            if (i === segmentCount) {
              // 最后一个点使用原始终点，保持所有属性
              result.push(point2);
            } else {
              // 创建插值点
              const newPoint = new PathPoint(pos.x, pos.y);
              newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;
              newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;
              newPoint.orientationType = point1.orientationType;
              newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;
              newPoint.isSubdivided = true;
              result.push(newPoint);
            }
          }

          return result;
        }
        /**
         * 平滑速度变化 - 后处理步骤
         */


        smoothSpeedTransitions(points) {
          if (points.length < 3) return; // 使用简单的移动平均来平滑速度变化

          const smoothingWindow = 3;
          const originalSpeeds = points.map(p => p.speed);

          for (let i = 1; i < points.length - 1; i++) {
            let sum = 0;
            let count = 0;

            for (let j = Math.max(0, i - smoothingWindow); j <= Math.min(points.length - 1, i + smoothingWindow); j++) {
              sum += originalSpeeds[j];
              count++;
            } // 只对插值点进行平滑，保持原始点的速度


            if (points[i].isSubdivided) {
              points[i].speed = sum / count;
            }
          }
        }
        /**
         * 均匀距离采样曲线段
         */


        sampleCurveSegmentUniformly(p0, p1, p2, p3, point1, point2, targetDistance) {
          const result = [];
          const avgSmoothness = (point1.smoothness + point2.smoothness) / 2; // 估算曲线长度（使用多个采样点）

          const estimatedLength = this.estimateCurveLength(p0, p1, p2, p3, avgSmoothness);

          if (estimatedLength <= targetDistance) {
            result.push(point2);
            return result;
          }

          const segmentCount = Math.ceil(estimatedLength / targetDistance); // 使用弧长参数化进行均匀采样

          const tValues = this.generateArcLengthParameterization(p0, p1, p2, p3, avgSmoothness, segmentCount);

          for (let i = 1; i <= segmentCount; i++) {
            const t = tValues[i];

            if (i === segmentCount) {
              // 最后一个点使用原始终点
              result.push(point2);
            } else {
              // 创建插值点
              const newPoint = PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t, avgSmoothness);
              result.push(newPoint);
            }
          }

          return result;
        }
        /**
         * 估算曲线长度
         */


        estimateCurveLength(p0, p1, p2, p3, smoothness) {
          let length = 0;
          const samples = 20; // 使用20个采样点估算长度

          let prevPos = PathData.catmullRomPoint(0, p0, p1, p2, p3, smoothness);

          for (let i = 1; i <= samples; i++) {
            const t = i / samples;
            const currentPos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
            length += Vec2.distance(prevPos, currentPos);
            prevPos = currentPos;
          }

          return length;
        }
        /**
         * 生成弧长参数化的t值数组
         */


        generateArcLengthParameterization(p0, p1, p2, p3, smoothness, segmentCount) {
          const tValues = [0]; // 起始点t=0

          const targetSegmentLength = this.estimateCurveLength(p0, p1, p2, p3, smoothness) / segmentCount;
          let currentLength = 0;
          let currentT = 0;
          let prevPos = PathData.catmullRomPoint(0, p0, p1, p2, p3, smoothness);
          const stepSize = 0.001; // 小步长用于精确计算

          for (let segment = 1; segment <= segmentCount; segment++) {
            const targetLength = segment * targetSegmentLength; // 寻找对应的t值

            while (currentLength < targetLength && currentT < 1) {
              currentT += stepSize;
              const currentPos = PathData.catmullRomPoint(currentT, p0, p1, p2, p3, smoothness);
              currentLength += Vec2.distance(prevPos, currentPos);
              prevPos = currentPos;
            }

            tValues.push(Math.min(currentT, 1));
          }

          return tValues;
        }
        /**
         * 原有的自适应细分策略 - 作为备选方案
         */


        generateAdaptiveSubdivisionPoints(effectivePoints) {
          const subdivided = [];
          const pointCount = effectivePoints.length; // 添加第一个点

          subdivided.push(effectivePoints[0]); // 计算需要处理的段数

          const segmentCount = this.closed ? pointCount : pointCount - 1; // 为每一段生成细分点

          for (let i = 0; i < segmentCount; i++) {
            const p0 = this.getControlPoint(effectivePoints, i - 1);
            const p1 = effectivePoints[i].position;
            const p2 = this.getControlPoint(effectivePoints, i + 1);
            const p3 = this.getControlPoint(effectivePoints, i + 2);
            const point = effectivePoints[i];
            const pointNext = effectivePoints[(i + 1) % pointCount];
            const startSmoothness = point.smoothness;
            const endSmoothness = pointNext.smoothness; // 如果任一端点的smoothness为0，则整段使用直线

            if (startSmoothness === 0 || endSmoothness === 0) {
              // 直线连接：只需要添加终点
              subdivided.push(pointNext);
            } else {
              // 使用自适应细分算法
              const segmentPoints = this.adaptiveSubdivision(p0, p1, p2, p3, point, pointNext);
              subdivided.push(...segmentPoints);
            }
          } // 处理闭合路径的重复点


          if (this.closed && subdivided.length > 1) {
            const firstPoint = subdivided[0];
            const lastPoint = subdivided[subdivided.length - 1];
            const distance = Vec2.distance(firstPoint.position, lastPoint.position);

            if (distance < 0.1) {
              subdivided.pop();
            }
          }

          return subdivided;
        }
        /**
         * 获取有效的路径点范围（考虑startIdx和endIdx）
         */


        getPoints() {
          if (this.points.length === 0) return [];
          const startIndex = Math.max(0, Math.min(this.startIdx, this.points.length - 1));
          const endIndex = this.endIdx === -1 ? this.points.length - 1 : Math.max(startIndex, Math.min(this.endIdx, this.points.length - 1));
          return this.points.slice(startIndex, endIndex + 1);
        }
        /**
         * 自适应细分算法 - 基于曲率和误差的智能细分
         * @param p0 前一个控制点
         * @param p1 起始点
         * @param p2 结束点
         * @param p3 后一个控制点
         * @param point1 起始PathPoint
         * @param point2 结束PathPoint
         * @returns 细分后的PathPoint数组
         */


        adaptiveSubdivision(p0, p1, p2, p3, point1, point2, maxDepth = 6) {
          const avgSmoothness = (point1.smoothness + point2.smoothness) / 2; // 如果平滑度为0，直接返回终点

          if (avgSmoothness === 0) {
            return [point2];
          } // 递归细分（从深度0开始），但不包括t=1的终点


          const subdivisionPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, 0, 0.999999, 0, maxDepth, avgSmoothness); // 最后添加原始的终点，确保保留所有原始属性（包括stayDuration等）

          subdivisionPoints.push(point2);
          return subdivisionPoints;
        }
        /**
         * 递归细分方法
         */


        static subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, t2, depth, maxDepth, smoothness) {
          // 达到最大深度，停止细分
          if (depth >= maxDepth) {
            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];
          }

          const tMid = (t1 + t2) / 2; // 计算三个点：起点、中点、终点

          const startPos = PathData.catmullRomPoint(t1, p0, p1, p2, p3, smoothness);
          const midPos = PathData.catmullRomPoint(tMid, p0, p1, p2, p3, smoothness);
          const endPos = PathData.catmullRomPoint(t2, p0, p1, p2, p3, smoothness); // 计算线性插值的中点

          const linearMid = Vec2.lerp(new Vec2(), startPos, endPos, 0.5); // 计算误差（曲线中点与线性中点的距离）

          const error = Vec2.distance(midPos, linearMid); // 计算曲率（使用三点法）

          const curvature = PathData.calculateCurvature(startPos, midPos, endPos); // 动态误差阈值：考虑距离和曲率

          const distance = Vec2.distance(startPos, endPos);
          const baseThreshold = Math.max(0.5, distance * 0.01); // 基础阈值

          const curvatureThreshold = baseThreshold * (1 + curvature * 10); // 曲率调整
          // console.log('error:', error, 'curvatureThreshold:', curvatureThreshold);
          // 如果误差小于阈值，不需要进一步细分

          if (error < curvatureThreshold) {
            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];
          } // 需要细分：递归处理两个子段


          const leftPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, tMid, depth + 1, maxDepth, smoothness);
          const rightPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, tMid, t2, depth + 1, maxDepth, smoothness);
          return [...leftPoints, ...rightPoints];
        }
        /**
         * 计算三点的曲率
         */


        static calculateCurvature(p1, p2, p3) {
          const v1 = Vec2.subtract(new Vec2(), p2, p1);
          const v2 = Vec2.subtract(new Vec2(), p3, p2); // 避免除零

          const len1 = v1.length();
          const len2 = v2.length();
          if (len1 < 0.001 || len2 < 0.001) return 0;
          v1.normalize();
          v2.normalize(); // 计算角度变化

          const dot = Vec2.dot(v1, v2);
          const clampedDot = Math.max(-1, Math.min(1, dot));
          const angle = Math.acos(clampedDot); // 归一化曲率值

          return angle / Math.PI;
        }
        /**
         * 创建曲线插值的PathPoint（使用Catmull-Rom曲线）
         */


        static createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t, smoothness) {
          // 使用Catmull-Rom曲线计算位置
          const pos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);
          const newPoint = new PathPoint(pos.x, pos.y); // 插值其他属性

          newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;
          newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;
          newPoint.orientationType = point1.orientationType;
          newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;
          newPoint.isSubdivided = true;
          return newPoint;
        }
        /**
         * 获取有效路径点的控制点（处理边界情况）
         */


        getControlPoint(effectivePoints, index) {
          const pointCount = effectivePoints.length;

          if (this.closed) {
            // 闭合路径，使用循环索引
            const wrappedIndex = (index % pointCount + pointCount) % pointCount;
            return effectivePoints[wrappedIndex].position;
          } else {
            // 开放路径，边界处理
            if (index < 0) {
              // 延伸第一个点
              const p0 = effectivePoints[0].position;
              const p1 = effectivePoints[1].position;
              return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
            } else if (index >= pointCount) {
              // 延伸最后一个点
              const p0 = effectivePoints[pointCount - 2].position;
              const p1 = effectivePoints[pointCount - 1].position;
              return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
            } else {
              return effectivePoints[index].position;
            }
          }
        }
        /**
         * 获取控制点（处理边界情况）- 保留用于兼容性
         */
        // private getControlPoint(index: number): Vec2 {
        //     const pointCount = this.points.length;
        //     if (this.closed) {
        //         // 闭合路径，使用循环索引
        //         const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;
        //         return this.points[wrappedIndex].position;
        //     } else {
        //         // 开放路径，边界处理
        //         if (index < 0) {
        //             // 延伸第一个点
        //             const p0 = this.points[0].position;
        //             const p1 = this.points[1].position;
        //             return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));
        //         } else if (index >= pointCount) {
        //             // 延伸最后一个点
        //             const p0 = this.points[pointCount - 2].position;
        //             const p1 = this.points[pointCount - 1].position;
        //             return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));
        //         } else {
        //             return this.points[index].position;
        //         }
        //     }
        // }

        /**
         * 自定义序列化 - 排除缓存数据
         */


        toJSON() {
          return {
            name: this.name,
            startIdx: this.startIdx,
            endIdx: this.endIdx,
            points: this.points,
            closed: this.closed
          };
        }
        /**
         * 自定义反序列化 - 清除缓存确保重新计算
         */


        fromJSON(data) {
          if (!data) return;
          this.name = data.name || "";
          this.startIdx = data.startIdx || 0;
          this.endIdx = data.endIdx || -1;
          this.points = data.points ? data.points.map(p => {
            const point = new PathPoint();
            point.fromJSON(p);
            return point;
          }) : [];
          this.closed = data.closed || false; // 清除缓存，确保使用新数据重新计算

          this._cachedSubdividedPoints = null;
        }
        /**
         * 静态工厂方法 - 从JSON创建PathData实例
         */


        static fromJSON(data) {
          const pathData = new PathData();
          pathData.fromJSON(data);
          return pathData;
        }

      }, (_descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "name", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "pathType", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return ePathType.Custom;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class5.prototype, "startIdx", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class5.prototype, "endIdx", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return -1;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class5.prototype, "points", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class5.prototype, "closed", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class5.prototype, "samplingStrategy", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eSamplingStrategy.UniformDistance;
        }
      })), _class5)) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=bb9b33b2273a99d958fbdd91f2e7ce7081eb88a7.js.map