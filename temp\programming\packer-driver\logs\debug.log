14:23:58.766 debug: 2025/10/4 14:23:58
14:23:58.767 debug: Project: E:\M2Game\Client
14:23:58.767 debug: Targets: editor,preview
14:23:58.769 debug: Incremental file seems great.
14:23:58.769 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
14:23:58.776 debug: Initializing target [Editor]
14:23:58.776 debug: Loading cache
14:23:58.787 debug: Loading cache costs 10.71939999999995ms.
14:23:58.787 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
14:23:58.787 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:23:58.788 debug: Initializing target [Preview]
14:23:58.788 debug: Loading cache
14:23:58.804 debug: Loading cache costs 15.744699999999739ms.
14:23:58.804 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:23:58.835 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tween,ui,video,websocket,webview,custom-pipeline
14:23:58.840 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "E:\\M2Game\\Client\\assets"
  },
  {
    "root": "db://i18n/",
    "physical": "E:\\M2Game\\Client\\extensions\\i18n\\assets",
    "jail": "E:\\M2Game\\Client\\extensions\\i18n\\assets"
  }
]
14:23:58.840 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/",
    "db://i18n/": "file:///E:/M2Game/Client/extensions/i18n/assets/"
  }
}
14:23:58.841 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/",
    "db://i18n/": "file:///E:/M2Game/Client/extensions/i18n/assets/"
  }
}
14:23:58.841 debug: Pulling asset-db.
14:23:58.877 debug: Fetch asset-db cost: 35.734500000000025ms.
14:23:58.878 debug: Build iteration starts.
Number of accumulated asset changes: 292
Feature changed: false
14:23:58.878 debug: Target(editor) build started.
14:23:58.880 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
