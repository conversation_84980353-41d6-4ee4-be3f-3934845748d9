System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Enum, JsonAsset, eMoveEvent, eOrientationType, PathData, DefaultMove, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _class3, _crd, degreesToRadians, radiansToDegrees, ccclass, property, executeInEditMode, PathMove;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDefaultMove(extras) {
    _reporterNs.report("DefaultMove", "./DefaultMove", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Enum = _cc.Enum;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      eMoveEvent = _unresolved_2.eMoveEvent;
      eOrientationType = _unresolved_2.eOrientationType;
    }, function (_unresolved_3) {
      PathData = _unresolved_3.PathData;
    }, function (_unresolved_4) {
      DefaultMove = _unresolved_4.DefaultMove;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0793c9EHOFKg6LLrnZPLKct", "PathMove", undefined);

      __checkObsolete__(['_decorator', 'Component', 'misc', 'Enum', 'Node', 'UITransform', 'Vec2', 'Vec3', 'JsonAsset']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("PathMove", PathMove = (_dec = ccclass('PathMove'), _dec2 = executeInEditMode(), _dec3 = property({
        type: JsonAsset,
        displayName: "路径数据(预览用)"
      }), _dec4 = property({
        displayName: "循环移动"
      }), _dec5 = property({
        displayName: "反向移动"
      }), _dec6 = property({
        type: Enum(_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
          error: Error()
        }), eOrientationType) : eOrientationType),
        displayName: "朝向类型"
      }), _dec7 = property({
        displayName: "朝向参数"
      }), _dec(_class = _dec2(_class = (_class2 = (_class3 = class PathMove extends (_crd && DefaultMove === void 0 ? (_reportPossibleCrUseOfDefaultMove({
        error: Error()
      }), DefaultMove) : DefaultMove) {
        constructor(...args) {
          super(...args);
          this._pathAsset = null;

          _initializerDefineProperty(this, "loop", _descriptor, this);

          _initializerDefineProperty(this, "reverse", _descriptor2, this);

          // 路径相关数据
          this._pathData = null;
          this._subdivided = [];
          // 细分后的路径点（包含完整信息）
          // 路径偏移
          this._offsetX = 0;
          this._offsetY = 0;
          // 移动状态
          this._currentPointIndex = 0;
          // 当前所在的细分点索引
          this._nextPointIndex = 0;
          this._remainDistance = 0;
          // 距离下一个点的剩余距离
          // 停留状态
          this._stayTimer = 0;
          // 停留计时器（秒）
          this._updateInEditor = false;
        }

        get pathAsset() {
          return this._pathAsset;
        }

        set pathAsset(value) {
          this._pathAsset = value;

          if (value) {
            this.setPath((_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).fromJSON(value.json));
          }
        }

        get editor_orientationType() {
          return this.orientationType;
        }

        set editor_orientationType(value) {
          this.setOrientation(value, this.editor_orientationParam);
        }

        get editor_orientationParam() {
          return this.orientationParam;
        }

        set editor_orientationParam(value) {
          this.setOrientation(this.orientationType, value);
        }

        static debugLog(message) {
          if (PathMove.DEBUG_ENABLED) {
            console.log(message);
          }
        }

        static debugWarn(message) {
          if (PathMove.DEBUG_ENABLED) {
            console.warn(message);
          }
        } // 公共方法：启用/禁用调试日志


        static setDebugEnabled(enabled) {
          PathMove.DEBUG_ENABLED = enabled;
          console.log(`[PathMove] 调试日志 ${enabled ? '启用' : '禁用'}`);
        } // 公共方法：设置调试阈值


        static setDebugThresholds(thresholds) {
          Object.assign(PathMove.DEBUG_THRESHOLDS, thresholds);
          console.log(`[PathMove] 调试阈值已更新:`, PathMove.DEBUG_THRESHOLDS);
        }

        onFocusInEditor() {
          this._updateInEditor = true;
          this._isMovable = true;
        }

        onLostFocusInEditor() {
          this._updateInEditor = false;
          this._isMovable = false;
          this.resetToStart();
        }

        update(dt) {
          if (this._updateInEditor) {
            this.tick(dt);
          }
        } // 注意调用顺序,先调用setOffset,再调用setPath


        setOffset(x, y) {
          this._offsetX = x;
          this._offsetY = y;
          return this;
        }

        setPath(pathData) {
          this._pathData = pathData; // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组

          this._subdivided = this._pathData.getSubdividedPoints();
          this.resetToStart();
          return this;
        }
        /**
         * 主要的移动更新逻辑
         */


        tick(dt) {
          if (!this._isMovable) return;

          if (!this._pathData) {
            super.tick(dt);
            return;
          } // [DEBUG] 记录基本状态


          if (dt > PathMove.DEBUG_THRESHOLDS.frameTime) {
            PathMove.debugWarn(`[PathMove] 帧时间过长: ${(dt * 1000).toFixed(2)}ms, 可能导致移动不平滑`);
          } // 处理停留逻辑


          if (this._stayTimer > 0) {
            this._stayTimer -= dt;

            if (this._stayTimer <= 0) {
              this._stayTimer = 0;
              PathMove.debugLog(`[PathMove] 停留结束，继续移动到下一个点`); // 停留结束，继续移动到下一个点

              this.moveToNextPoint();
            }
          } else if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
          } // [DEBUG] 记录tilting状态


          const positionBeforeTilt = {
            x: this._position.x,
            y: this._position.y
          };
          this.updateTilting(this.speedAngle, dt, this._position);

          if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
            const tiltDeltaX = this._position.x - positionBeforeTilt.x;
            const tiltDeltaY = this._position.y - positionBeforeTilt.y;

            if (Math.abs(tiltDeltaX) > PathMove.DEBUG_THRESHOLDS.tiltOffset || Math.abs(tiltDeltaY) > PathMove.DEBUG_THRESHOLDS.tiltOffset) {
              PathMove.debugLog(`[PathMove] Tilting偏移: (${tiltDeltaX.toFixed(2)}, ${tiltDeltaY.toFixed(2)}), tiltSpeed: ${this.tiltSpeed}, tiltOffset: ${this.tiltOffset}`);
            }
          }

          if (Math.abs(this.speed) > 0.001 || Math.abs(this.tiltSpeed) > 0.001) {
            // 设置节点位置
            this.node.setPosition(this._position);
            this.checkVisibility(); // 更新朝向

            this.updateOrientation(dt);
          }
        }

        tickMovement(dt) {
          // 使用匀加速直线运动更新位置
          const v0 = this.speed; // s = v0*t + 0.5*a*t^2

          let s = v0 * dt + 0.5 * this.acceleration * dt * dt; // [DEBUG] 记录运动计算

          const originalS = s;
          const originalDt = dt;

          if (s > this._remainDistance) {
            s = this._remainDistance; // 重新计算实际需要的时间

            if (v0 + this.acceleration * dt !== 0) {
              dt = s / (v0 + 0.5 * this.acceleration * dt);
            }

            PathMove.debugLog(`[PathMove] 距离限制: 原始移动距离 ${originalS.toFixed(2)} -> 实际移动距离 ${s.toFixed(2)}, dt: ${originalDt.toFixed(4)} -> ${dt.toFixed(4)}`);
          }

          const newSpeed = this.speed + this.acceleration * dt; // [DEBUG] 记录速度和加速度变化

          if (Math.abs(this.acceleration) > 0.1) {
            PathMove.debugLog(`[PathMove] 运动状态: v0=${v0.toFixed(2)}, a=${this.acceleration.toFixed(2)}, s=${s.toFixed(2)}, newSpeed=${newSpeed.toFixed(2)}, remainDist=${this._remainDistance.toFixed(2)}`);
          }

          this.speed = newSpeed; // 计算移动向量

          const angleRad = this.speedAngle;
          const deltaX = Math.cos(angleRad) * s;
          const deltaY = Math.sin(angleRad) * s; // [DEBUG] 记录位置变化 (暂时注释掉未使用的变量)
          // const oldPos = { x: this._position.x, y: this._position.y };
          // 更新位置

          this._position.x += deltaX;
          this._position.y += deltaY; // [DEBUG] 检查位置变化是否异常

          const positionDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

          if (positionDelta > PathMove.DEBUG_THRESHOLDS.singleFrameMove) {
            PathMove.debugWarn(`[PathMove] 单帧移动距离过大: ${positionDelta.toFixed(2)}, delta=(${deltaX.toFixed(2)}, ${deltaY.toFixed(2)}), 角度=${(angleRad * 180 / Math.PI).toFixed(2)}°`);
          } // 检查是否到达目标点


          if (this._remainDistance > 0) {
            this._remainDistance -= s;

            if (this._remainDistance <= 0) {
              // 当剩余距离为负数时，说明我们超过了目标点
              // 需要回退到精确的目标点位置
              const overshoot = -this._remainDistance;

              if (overshoot > 0.1) {
                // 如果超调超过0.1像素
                PathMove.debugLog(`[PathMove] 超调目标点 ${overshoot.toFixed(2)} 像素，进行位置校正`); // 回退超调的距离

                const angleRad = this.speedAngle;
                this._position.x -= Math.cos(angleRad) * overshoot;
                this._position.y -= Math.sin(angleRad) * overshoot;
              }

              PathMove.debugLog(`[PathMove] 到达目标点 ${this._nextPointIndex}, 剩余距离: ${this._remainDistance.toFixed(2)}`);
              this._remainDistance = 0; // 确保剩余距离为0

              this.onReachPoint(this._nextPointIndex);
            }
          }
        }

        getDesiredOrientation(dt) {
          if (this._pathData && this.orientationType === (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
            error: Error()
          }), eOrientationType) : eOrientationType).Path) {
            // PathMove这里speedAngle是radians，需要转换为degrees进行角度lerp
            const targetAngleDegrees = radiansToDegrees(this.speedAngle);
            const oldOrientation = this.orientation;
            const newOrientation = PathMove.lerpAngle(this.orientation, targetAngleDegrees, PathMove.kLerpFactor, dt); // [DEBUG] 记录朝向变化

            const orientationDiff = Math.abs(newOrientation - oldOrientation);

            if (orientationDiff > PathMove.DEBUG_THRESHOLDS.orientationChange) {
              PathMove.debugLog(`[PathMove] 朝向变化: ${oldOrientation.toFixed(2)}° -> ${newOrientation.toFixed(2)}° (目标: ${targetAngleDegrees.toFixed(2)}°), 变化量: ${orientationDiff.toFixed(2)}°`);
            }

            return newOrientation;
          }

          return super.getDesiredOrientation(dt);
        }

        onReachPoint(pointIndex) {
          // 更新当前点索引
          this._currentPointIndex = pointIndex; // [DEBUG] 记录到达点信息

          PathMove.debugLog(`[PathMove] 到达路径点 ${pointIndex}`); // 检查是否需要停留

          const currentPoint = this.getPathPoint(pointIndex);

          if (currentPoint) {
            // [DEBUG] 记录位置更新
            const oldBasePos = {
              x: this._basePosition.x,
              y: this._basePosition.y
            };
            const targetX = currentPoint.x + this._offsetX;
            const targetY = currentPoint.y + this._offsetY;
            const positionJump = Math.sqrt(Math.pow(targetX - oldBasePos.x, 2) + Math.pow(targetY - oldBasePos.y, 2)); // 如果位置跳跃过大，使用平滑过渡而不是直接跳跃

            if (positionJump > PathMove.DEBUG_THRESHOLDS.positionJump) {
              PathMove.debugWarn(`[PathMove] 检测到位置跳跃: ${positionJump.toFixed(2)}, 从 (${oldBasePos.x.toFixed(2)}, ${oldBasePos.y.toFixed(2)}) 到 (${targetX.toFixed(2)}, ${targetY.toFixed(2)}), 使用平滑过渡`); // 使用当前位置作为基础位置，避免突然跳跃
              // 这样可以保持移动的连续性

              this._basePosition.x = this._position.x;
              this._basePosition.y = this._position.y;
            } else {
              // 位置跳跃较小，可以直接设置
              this._basePosition.x = targetX;
              this._basePosition.y = targetY;

              if (positionJump > PathMove.DEBUG_THRESHOLDS.positionAdjust) {
                PathMove.debugLog(`[PathMove] 位置微调: ${positionJump.toFixed(2)}, 从 (${oldBasePos.x.toFixed(2)}, ${oldBasePos.y.toFixed(2)}) 到 (${targetX.toFixed(2)}, ${targetY.toFixed(2)})`);
              }
            } // 设置速度


            const oldSpeed = this.speed;
            this.speed = currentPoint.speed;

            if (Math.abs(this.speed - oldSpeed) > PathMove.DEBUG_THRESHOLDS.speedChange) {
              PathMove.debugLog(`[PathMove] 速度变化: ${oldSpeed.toFixed(2)} -> ${this.speed.toFixed(2)}`);
            } // 设置朝向


            this.orientationType = currentPoint.orientationType;
            this.orientationParam = currentPoint.orientationParam; // 这里要考虑的问题是: 第一个点的初始朝向,希望是立刻生效,而不是lerp

            if (pointIndex === 0) {
              // 对于第一个点，检查是否需要位置跳跃
              const currentPos = this.node.getPosition();
              const targetPos = this._basePosition;
              const initJump = Math.sqrt(Math.pow(targetPos.x - currentPos.x, 2) + Math.pow(targetPos.y - currentPos.y, 2));

              if (initJump > 5) {
                PathMove.debugLog(`[PathMove] 初始化位置跳跃: ${initJump.toFixed(2)}, 从 (${currentPos.x.toFixed(2)}, ${currentPos.y.toFixed(2)}) 到 (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)})`);
              }

              this._position.set(this._basePosition);

              this.node.setPosition(this._position);
              PathMove.debugLog(`[PathMove] 初始化位置: (${this._position.x.toFixed(2)}, ${this._position.y.toFixed(2)})`);
              const nextPoint = this.getPathPoint(pointIndex + 1);

              if (nextPoint) {
                const dirX = nextPoint.x - currentPoint.x;
                const dirY = nextPoint.y - currentPoint.y;
                this.speedAngle = Math.atan2(dirY, dirX);
                PathMove.debugLog(`[PathMove] 初始化角度: ${(this.speedAngle * 180 / Math.PI).toFixed(2)}°`);

                if (this.orientationType === (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
                  error: Error()
                }), eOrientationType) : eOrientationType).Path) {
                  this.orientation = radiansToDegrees(this.speedAngle);
                }
              }

              this.updateOrientation(0);
            }

            PathMove.debugLog(`[PathMove] 到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);

            if (currentPoint.stayDuration > 0) {
              this._stayTimer = currentPoint.stayDuration / 1000.0;
              return;
            }
          } else {
            PathMove.debugWarn(`[PathMove] 无法获取路径点 ${pointIndex}, 停止移动`);
            this.speed = 0;
          } // 继续移动到下一个点


          this.moveToNextPoint();
        }

        moveToNextPoint() {
          const nextIndex = this._currentPointIndex + 1;

          if (nextIndex >= this._subdivided.length) {
            // 到达路径终点
            if (this.loop) {
              // 循环模式，回到起点
              this.setNext(0);
              this.emit((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
                error: Error()
              }), eMoveEvent) : eMoveEvent).onPathLoop);
            } else {
              // 停止移动
              this._nextPointIndex = this._currentPointIndex;
              this.emit((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
                error: Error()
              }), eMoveEvent) : eMoveEvent).onPathEnd);
            }
          } else {
            // 移动到下一个点
            this.setNext(nextIndex);
          }
        }

        setNext(pathPointIndex) {
          this._nextPointIndex = pathPointIndex;
          const currentPoint = this.getPathPoint(this._currentPointIndex);
          const nextPoint = this.getPathPoint(this._nextPointIndex);

          if (currentPoint && nextPoint) {
            // 使用当前实际位置而不是路径点位置来计算距离
            // 这样可以减少累积误差
            const currentX = this._position.x;
            const currentY = this._position.y;
            const targetX = nextPoint.x + this._offsetX;
            const targetY = nextPoint.y + this._offsetY;
            const dirX = targetX - currentX;
            const dirY = targetY - currentY;
            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);

            if (this._remainDistance > 1) {
              // 计算目标移动角度
              const newAngle = Math.atan2(dirY, dirX);
              const oldAngle = this.speedAngle; // [DEBUG] 检查角度变化是否过大

              let angleDiff = newAngle - oldAngle; // 标准化角度差到 [-π, π] 范围

              while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;

              while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;

              if (Math.abs(angleDiff) > PathMove.DEBUG_THRESHOLDS.angleChange) {
                PathMove.debugWarn(`[PathMove] 角度变化过大: ${(angleDiff * 180 / Math.PI).toFixed(2)}°, 从 ${(oldAngle * 180 / Math.PI).toFixed(2)}° 到 ${(newAngle * 180 / Math.PI).toFixed(2)}°`);
              }

              this.speedAngle = newAngle; // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x
              // 解出 a = (v1^2 - v0^2) / (2*x)

              const v0 = currentPoint.speed;
              const v1 = nextPoint.speed;
              this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance); // [DEBUG] 记录路径段设置

              PathMove.debugLog(`[PathMove] 设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 实际距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${(newAngle * 180 / Math.PI).toFixed(2)}°`); // [DEBUG] 检查加速度是否异常

              if (Math.abs(this.acceleration) > PathMove.DEBUG_THRESHOLDS.acceleration) {
                PathMove.debugWarn(`[PathMove] 加速度过大: ${this.acceleration.toFixed(2)}, 可能导致移动不平滑`);
              }
            } else {
              PathMove.debugLog(`[PathMove] 距离过小 (${this._remainDistance.toFixed(2)}), 跳过此段`);
            }
          }
        }

        getPathPoint(pathPointIndex) {
          if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
          }

          return this._subdivided[pathPointIndex];
        }

        resetToStart() {
          this._currentPointIndex = 0;
          this._nextPointIndex = 0;
          this._stayTimer = 0;
          this._tiltTime = 0;
          this._remainDistance = 0;
          this.speed = 0;
          this.acceleration = 0;
          this.tiltSpeed = 0;
          this.tiltOffset = 0;
          this.onReachPoint(0);
        }

        isStaying() {
          return this._stayTimer > 0;
        }

        getRemainingStayTime() {
          return this._stayTimer;
        } // static lerp(a: number, b: number, decay: number, dt: number): number {
        //     return (a - b) * Math.exp(-decay * dt) + b;
        // }

        /**
         * 角度插值，正确处理角度环绕问题
         * @param from 起始角度（度）
         * @param to 目标角度（度）
         * @param decay 衰减系数
         * @param dt 时间增量
         * @returns 插值后的角度（度）
         */


        static lerpAngle(from, to, decay, dt) {
          // 将角度标准化到[-180, 180]范围
          const normalizeAngle = angle => {
            while (angle > 180) angle -= 360;

            while (angle < -180) angle += 360;

            return angle;
          }; // 标准化输入角度


          from = normalizeAngle(from);
          to = normalizeAngle(to); // 计算角度差，选择最短路径

          let diff = to - from;
          if (diff > 180) diff -= 360;
          if (diff < -180) diff += 360; // 使用指数衰减插值

          const lerpedDiff = diff * (1 - Math.exp(-decay * dt));
          return normalizeAngle(from + lerpedDiff);
        }

      }, _class3.kLerpFactor = 16, _class3.DEBUG_ENABLED = true, _class3.DEBUG_THRESHOLDS = {
        frameTime: 0.02,
        // 帧时间阈值（秒）
        positionJump: 10,
        // 位置跳跃阈值（像素）
        positionAdjust: 2,
        // 位置微调阈值（像素）
        singleFrameMove: 50,
        // 单帧移动距离阈值（像素）
        angleChange: Math.PI / 4,
        // 角度变化阈值（弧度，45度）
        orientationChange: 10,
        // 朝向变化阈值（度）
        acceleration: 2000,
        // 加速度阈值
        speedChange: 50,
        // 速度变化阈值
        tiltOffset: 1 // tilting偏移阈值（像素）

      }, _class3), (_applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "pathAsset"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "loop", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "reverse", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "editor_orientationType", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "editor_orientationType"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "editor_orientationParam", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "editor_orientationParam"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=794ce2bb3f0615a636a10a8c3f025e15162051e7.js.map