'use strict';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
const path_1 = __importDefault(require("path"));
exports.template = `
<div class="component-container"></div>

<ui-prop>
    <ui-label slot="label">请从下拉框中选择:</ui-label>
    <ui-select slot="content" class="prefab-dropdown"></ui-select>
</ui-prop>
<ui-prop>
    <ui-button class="btn-preview" style="display: none;">添加到场景</ui-button>
    <ui-button class="btn-new">新建emitter</ui-button>
    <ui-button class="btn-copy" style="display: none;">从选择复制新emitter</ui-button>
    <ui-button class="btn-save" style="display: none;">保存所有</ui-button>
    <ui-button class="btn-reset" style="display: none;">重置预览</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    prefabDropdown: '.prefab-dropdown',
    btnPreview: '.btn-preview',
    btnNew: '.btn-new',
    btnCopy: '.btn-copy',
    btnSave: '.btn-save',
    btnReset: '.btn-reset',
};
const emitterDir = 'db://assets/resources/game/prefabs/emitter/';
let prefabList = [];
let selectedPrefab = null;
async function loadPrefabList() {
    const pattern = `${emitterDir}**/*.prefab`;
    try {
        // @ts-ignore
        const res = await Editor.Message.request('asset-db', 'query-assets', { pattern });
        const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
        const prefabs = arr
            .filter((a) => a && !a.isDirectory && a.name.endsWith('.prefab'))
            .map((a) => ({
            name: String(a.name || '').replace(/\.prefab$/i, ''),
            path: a.path || '',
            uuid: a.uuid || ''
        }))
            .filter(p => p.name)
            .sort((a, b) => a.name.localeCompare(b.name));
        return prefabs;
    }
    catch (e) {
        console.warn('loadPrefabList failed', e);
        return [];
    }
}
function updateButtonVisibility() {
    var _a, _b;
    const hasSelection = selectedPrefab !== null;
    this.$.btnPreview.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnCopy.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnReset.style.display = ((_b = (_a = this.dump) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.uuid) ? 'inline-block' : 'none';
}
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    // Load prefab list
    prefabList = await loadPrefabList();
    // Setup dropdown options
    const dropdown = this.$.prefabDropdown;
    dropdown.innerHTML = '';
    if (prefabList.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '(无可用的prefab文件)';
        option.disabled = true;
        dropdown.appendChild(option);
    }
    else {
        // Add prefab options
        prefabList.forEach(prefab => {
            const option = document.createElement('option');
            option.value = prefab.uuid;
            option.textContent = prefab.name;
            dropdown.appendChild(option);
        });
    }
    // Handle dropdown selection change
    dropdown.addEventListener('change', () => {
        const selectedUuid = dropdown.value;
        selectedPrefab = prefabList.find(p => p.uuid === selectedUuid) || null;
        updateButtonVisibility.call(this);
    });
    // Handle preview button
    this.$.btnPreview.addEventListener('click', () => {
        var _a;
        if (selectedPrefab) {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'instantiatePrefab',
                args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid, selectedPrefab.uuid]
            });
        }
    });
    // Handle new emitter button
    this.$.btnNew.addEventListener('click', async () => {
        console.log('Create new emitter');
        // @ts-ignore
        const dirPath = path_1.default.join(Editor.Project.path, "assets", "resources", "game", "prefabs", "emitter");
        // @ts-ignore
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Prefab', extensions: ['prefab'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path_1.default.relative(dirPath, retData.filePath);
        const nameWithoutExt = name.replace(/\.prefab$/i, '');
        console.log('New emitter name:', name);
        const filePath = `${emitterDir}${name}`;
        try {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'createNewEmitter',
                args: [nameWithoutExt, filePath]
            });
            // Refresh prefab list
            prefabList = await loadPrefabList();
        }
        catch (e) {
            console.error('Failed to create new emitter:', e);
        }
    });
    // Handle copy emitter button
    this.$.btnCopy.addEventListener('click', async () => {
        var _a;
        if (!selectedPrefab)
            return;
        const sourceUrl = selectedPrefab.path;
        const targetUrl = sourceUrl + '_copy';
        const nameWithoutExt = selectedPrefab.name + '_copy';
        console.log('Copy emitter from ', sourceUrl, ' to ', targetUrl);
        try {
            // @ts-ignore
            await Editor.Message.request('asset-db', 'copy-asset', sourceUrl + '.prefab', targetUrl + '.prefab');
            // Refresh prefab list
            prefabList = await loadPrefabList();
            selectedPrefab = prefabList.find(p => p.name === nameWithoutExt) || null;
            if (selectedPrefab) {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'emitter-editor',
                    method: 'instantiatePrefab',
                    args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid, selectedPrefab.uuid]
                });
            }
        }
        catch (e) {
            console.error('Failed to copy emitter:', e);
        }
    });
    this.$.btnSave.addEventListener('click', async () => {
        // // @ts-ignore
        // Editor.Message.request('scene', 'execute-scene-script', {
        //     name: 'emitter-editor',
        //     method: 'saveAll',
        //     args: []
        // });
    });
    // Handle reset preview button
    this.$.btnReset.addEventListener('click', () => {
        var _a, _b;
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'replay',
            args: [(_b = (_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid) === null || _b === void 0 ? void 0 : _b.value]
        });
    });
    // Initial button visibility update
    updateButtonVisibility.call(this);
}
//# sourceMappingURL=data:application/json;base64,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