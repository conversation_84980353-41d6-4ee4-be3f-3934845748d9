import { _decorator, Component } from 'cc';
import { PathMove } from './PathMove';

const { ccclass, property } = _decorator;

/**
 * PathMove 调试助手
 * 用于快速启用/禁用 PathMove 的调试日志，并配置调试阈值
 */
@ccclass('PathMoveDebugHelper')
export class PathMoveDebugHelper extends Component {

    @property({ displayName: "启用调试日志" })
    public enableDebug: boolean = true;

    @property({ displayName: "位置跳跃阈值(像素)", tooltip: "超过此值会警告位置跳跃" })
    public positionJumpThreshold: number = 10;

    @property({ displayName: "单帧移动阈值(像素)", tooltip: "超过此值会警告单帧移动过大" })
    public singleFrameMoveThreshold: number = 50;

    @property({ displayName: "加速度阈值", tooltip: "超过此值会警告加速度过大" })
    public accelerationThreshold: number = 2000;

    @property({ displayName: "帧时间阈值(毫秒)", tooltip: "超过此值会警告帧时间过长" })
    public frameTimeThreshold: number = 20;

    onLoad() {
        // 设置调试状态
        PathMove.setDebugEnabled(this.enableDebug);

        // 设置调试阈值
        this.updateThresholds();
    }

    /**
     * 更新调试阈值
     */
    public updateThresholds() {
        PathMove.setDebugThresholds({
            positionJump: this.positionJumpThreshold,
            singleFrameMove: this.singleFrameMoveThreshold,
            acceleration: this.accelerationThreshold,
            frameTime: this.frameTimeThreshold / 1000 // 转换为秒
        });
    }

    /**
     * 在运行时切换调试状态
     */
    public toggleDebug() {
        this.enableDebug = !this.enableDebug;
        PathMove.setDebugEnabled(this.enableDebug);
    }

    /**
     * 启用调试日志
     */
    public enableDebugLog() {
        this.enableDebug = true;
        PathMove.setDebugEnabled(true);
    }

    /**
     * 禁用调试日志
     */
    public disableDebugLog() {
        this.enableDebug = false;
        PathMove.setDebugEnabled(false);
    }

    /**
     * 设置为高敏感度（更容易触发警告）
     */
    public setHighSensitivity() {
        this.positionJumpThreshold = 5;
        this.singleFrameMoveThreshold = 30;
        this.accelerationThreshold = 1000;
        this.frameTimeThreshold = 16;
        this.updateThresholds();
    }

    /**
     * 设置为低敏感度（不容易触发警告）
     */
    public setLowSensitivity() {
        this.positionJumpThreshold = 20;
        this.singleFrameMoveThreshold = 100;
        this.accelerationThreshold = 5000;
        this.frameTimeThreshold = 33;
        this.updateThresholds();
    }
}
