{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts"], "names": ["AttributeData", "MyApp", "AttributeConst", "DamageType", "constructor", "_baseAttributes", "Map", "_Attributes", "_AttributeModifies", "_sources", "addBaseAttribute", "key", "value", "set", "get", "recalculateAttributes", "addModify", "id", "modify", "source", "Set", "add", "removeModify", "for<PERSON>ach", "delete", "finalValue", "getFinalAttributeByKey", "getFinialAttributeByOutInKey", "out<PERSON><PERSON><PERSON><PERSON>", "out<PERSON><PERSON><PERSON><PERSON>", "inAdd<PERSON>ey", "in<PERSON>er<PERSON>ey", "CalcBulletDamage", "attacker", "defender", "hurtRate", "isNuclear", "damageType", "isBoss", "attackPowerFix", "attackerAttr", "attribute", "<PERSON><PERSON><PERSON><PERSON>", "attack", "getAttack", "damageAttackOutAddKey", "damageAttackOutPerKey", "damageAttackInAddKey", "damageAttackInPerKey", "EXPLOSIVE", "ExplosiveBulletAttackInAdd", "ExplosiveBulletAttackInPer", "ExplosiveBulletAttackOutAdd", "ExplosiveBulletAttackOutPer", "NORMAL", "NormalBulletAttackInAdd", "NormalBulletAttackInPer", "NormalBulletAttackOutAdd", "NormalBulletAttackOutPer", "ENERGETIC", "EnergeticBulletAttackInAdd", "EnergeticBulletAttackInPer", "EnergeticBulletAttackOutAdd", "EnergeticBulletAttackOutPer", "PHYSICAL", "PhysicsBulletAttackInAdd", "PhysicsBulletAttackInPer", "PhysicsBulletAttackOutAdd", "PhysicsBulletAttackOutPer", "ALL", "BulletAttackOutAdd", "BulletAttackOutPer", "BulletAttackInAdd", "BulletAttackInPer", "NuclearAttackOutAdd", "NuclearAttackOutPer", "NuclearAttackInAdd", "NuclearAttackInPer", "hurtResistanceOutAddKey", "NuclearHurtResistanceOutAdd", "BulletHurtResistanceOutAdd", "hurtResistanceOutPerKey", "NuclearHurtResistanceOutPer", "BulletHurtResistanceOutPer", "hurtResistanceInAddKey", "NuclearHurtResistanceInAdd", "BulletHurtResistanceInAdd", "hurtResistanceInPerKey", "NuclearHurtResistanceInPer", "BulletHurtResistanceInPer", "hurtResistance", "hurtDerate", "BulletHurtDerateOut", "BulletHurtDerateIn", "hurtBonus", "BossHurtBonusOut", "BossHurtBonusIn", "NormalHurtBonusOut", "NormalHurtBonusIn", "hurtFix", "damageHurtFixOutKey", "damageHurtFixInKey", "ExplosiveBulletHurtFixOut", "ExplosiveBulletHurtFixIn", "NormalBulletHurtFixOut", "NormalBulletHurtFixIn", "EnergeticBulletHurtFixOut", "EnergeticBulletHurtFixIn", "PhysicsBulletHurtFixOut", "PhysicsBulletHurtFixIn", "BulletHurtFixOut", "BulletHurtFixIn", "NuclearHurtFixOut", "NuclearHurtFixIn", "damage", "buff<PERSON><PERSON>p", "<PERSON><PERSON><PERSON>", "lubanTables", "TbGlobalAttr", "BulletPenetrationFlagBuffID", "penetration", "BulletPenetrationOut", "BulletPenetrationIn", "Math", "ceil", "getMaxHP", "floor", "MaxHPOutAdd", "MaxHPOutPer", "MaxHPInAdd", "MaxHPInPer", "AttackOutAdd", "AttackOutPer", "AttackInAdd", "AttackInPer", "getHPRecovery", "HPRecoveryOutAdd", "HPRecoveryOutPer", "HPRecoveryInAdd", "HPRecoveryInPer", "MaxHPRecoveryRateOut", "MaxHPRecoveryRateIn"], "mappings": ";;;iEAKaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;AALJC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;+BAGIH,a,GAAN,MAAMA,aAAN,CAAoB;AAIwC;AAE/DI,QAAAA,WAAW,GAAG;AAAA,eALNC,eAKM,GALyC,IAAIC,GAAJ,EAKzC;AALoD;AAKpD,eAJNC,WAIM,GAJqC,IAAID,GAAJ,EAIrC;AAJgD;AAIhD,eAHNE,kBAGM,GAHyD,IAAIF,GAAJ,EAGzD;AAAA,eAFNG,QAEM,GAFwC,IAAIH,GAAJ,EAExC;AACb;;AAEDI,QAAAA,gBAAgB,CAACC,GAAD,EAAsBC,KAAtB,EAAqC;AACjD,eAAKP,eAAL,CAAqBQ,GAArB,CAAyBF,GAAzB,EAA8B,KAAKN,eAAL,CAAqBS,GAArB,CAAyBH,GAAzB,KAA+B,IAAIC,KAAjE;;AACA,eAAKG,qBAAL,CAA2BJ,GAA3B;AACH,SAZsB,CAcvB;;;AACAK,QAAAA,SAAS,CAACC,EAAD,EAAaN,GAAb,EAAiCC,KAAjC,EAAgD;AACrD,cAAIM,MAAM,GAAG,KAAKV,kBAAL,CAAwBM,GAAxB,CAA4BH,GAA5B,CAAb;;AACA,cAAI,CAACO,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,IAAIZ,GAAJ,EAAT;;AACA,iBAAKE,kBAAL,CAAwBK,GAAxB,CAA4BF,GAA5B,EAAiCO,MAAjC;AACH;;AACDA,UAAAA,MAAM,CAACL,GAAP,CAAWI,EAAX,EAAeL,KAAf;;AAEA,cAAIO,MAAM,GAAG,KAAKV,QAAL,CAAcK,GAAd,CAAkBG,EAAlB,CAAb;;AACA,cAAI,CAACE,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,IAAIC,GAAJ,EAAT;;AACA,iBAAKX,QAAL,CAAcI,GAAd,CAAkBI,EAAlB,EAAsBE,MAAtB;AACH;;AACDA,UAAAA,MAAM,CAACE,GAAP,CAAWV,GAAX;AAEA,eAAKI,qBAAL,CAA2BJ,GAA3B;AACH,SA/BsB,CAiCvB;;;AACAW,QAAAA,YAAY,CAACL,EAAD,EAAa;AACrB,cAAIE,MAAM,GAAG,KAAKV,QAAL,CAAcK,GAAd,CAAkBG,EAAlB,CAAb;;AACAE,UAAAA,MAAM,QAAN,IAAAA,MAAM,CAAEI,OAAR,CAAiBZ,GAAD,IAAS;AACrB,gBAAIO,MAAM,GAAG,KAAKV,kBAAL,CAAwBM,GAAxB,CAA4BH,GAA5B,CAAb;;AACA,gBAAIO,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACM,MAAP,CAAcP,EAAd;AACA,mBAAKF,qBAAL,CAA2BJ,GAA3B;AACH;AACJ,WAND;;AAOA,eAAKF,QAAL,CAAce,MAAd,CAAqBP,EAArB;AACH,SA5CsB,CA8CvB;;;AACQF,QAAAA,qBAAqB,CAACJ,GAAD,EAAqB;AAAA;;AAC9C,cAAIc,UAAU,GAAG,KAAKpB,eAAL,CAAqBS,GAArB,CAAyBH,GAAzB,KAAiC,CAAlD,CAD8C,CAG9C;;AACA,wCAAKH,kBAAL,CAAwBM,GAAxB,CAA4BH,GAA5B,oCAAkCY,OAAlC,CAA0CF,GAAG,IAAI;AAC7CI,YAAAA,UAAU,IAAIJ,GAAd;AACH,WAFD,EAJ8C,CAQ9C;;AACA,eAAKd,WAAL,CAAiBM,GAAjB,CAAqBF,GAArB,EAA0Bc,UAA1B;AACH;;AAEDC,QAAAA,sBAAsB,CAACf,GAAD,EAA4B;AAC9C,iBAAO,KAAKJ,WAAL,CAAiBO,GAAjB,CAAqBH,GAArB,KAA6B,CAApC;AACH;;AAEDgB,QAAAA,4BAA4B,CAACC,SAAD,EAA2BC,SAA3B,EACxBC,QADwB,EACCC,QADD,EACkC;AAE1D,iBAAO,CAAC,KAAKL,sBAAL,CAA4BE,SAA5B,KAA0C,IAAI,KAAKF,sBAAL,CAA4BG,SAA5B,IAAyC,KAAvF,IACF,KAAKH,sBAAL,CAA4BI,QAA5B,CADC,KACyC,IAAI,KAAKJ,sBAAL,CAA4BK,QAA5B,IAAwC,KADrF,CAAP;AAEH;;AAEsB,eAAhBC,gBAAgB,CAACC,QAAD,EAAsBC,QAAtB,EACfC,QADe,EACGC,SADH,EACuBC,UADvB,EAC+CC,MAD/C,EACgEC,cADhE,EAC+F;AAElH,gBAAMC,YAAY,GAAGP,QAAQ,CAACQ,SAA9B;AACA,gBAAMC,YAAY,GAAGR,QAAQ,CAACO,SAA9B,CAHkH,CAIlH;AACA;;AACA,cAAIE,MAAM,GAAGH,YAAY,CAACI,SAAb,EAAb,CANkH,CAQlH;;AACA,cAAI,CAACR,SAAL,EAAgB;AACZ,gBAAIS,qBAAqB,GAAG,CAA5B;AAA8B;AAC9B,gBAAIC,qBAAqB,GAAG,CAA5B;AAA8B;AAC9B,gBAAIC,oBAAoB,GAAI,CAA5B;AAA8B;AAC9B,gBAAIC,oBAAoB,GAAI,CAA5B;AAA8B;;AAC9B,oBAAOX,UAAP;AACI,mBAAK;AAAA;AAAA,4CAAWY,SAAhB;AACIJ,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeK,0BAAvC;AACAJ,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeK,0BAAvC;AACAJ,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeK,2BAAvC;AACAJ,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeK,2BAAvC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWC,MAAhB;AACIT,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeU,uBAAvC;AACAT,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeU,uBAAvC;AACAT,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeU,wBAAvC;AACAT,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeU,wBAAvC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWC,SAAhB;AACId,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAee,0BAAvC;AACAd,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAee,0BAAvC;AACAd,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAee,2BAAvC;AACAd,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAee,2BAAvC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWC,QAAhB;AACInB,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeoB,wBAAvC;AACAnB,gBAAAA,qBAAqB,GAAG;AAAA;AAAA,sDAAeoB,wBAAvC;AACAnB,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeoB,yBAAvC;AACAnB,gBAAAA,oBAAoB,GAAI;AAAA;AAAA,sDAAeoB,yBAAvC;AACA;AAxBR;;AA0BA,gBAAI/B,UAAU,IAAI;AAAA;AAAA,0CAAWgC,GAA7B,EAAkC;AAC9B1B,cAAAA,MAAM,GAAG,CAAE,CAACA,MAAM,GAAGR,QAAT,GAAoBK,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe4C,kBAAnD,CAApB,GACE9B,YAAY,CAACd,sBAAb,CAAoCmB,qBAApC,CADH,KAEA,IAAI,CAACL,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe6C,kBAAnD,IACF/B,YAAY,CAACd,sBAAb,CAAoCoB,qBAApC,CADC,IAC6D,KAHjE,CAAD,GAIJN,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe8C,iBAAnD,CAJI,GAKJhC,YAAY,CAACd,sBAAb,CAAoCqB,oBAApC,CALG,KAMF,IAAI,CAACP,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe+C,iBAAnD,IACFjC,YAAY,CAACd,sBAAb,CAAoCsB,oBAApC,CADC,IAC4D,KAP9D,CAAT;AAQH,aATD,MASO;AACHL,cAAAA,MAAM,GAAG,CAAE,CAACA,MAAM,GAAGR,QAAT,GAAoBK,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe4C,kBAAnD,CAArB,KACA,IAAI9B,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe6C,kBAAnD,IAAyE,KAD7E,CAAD,GAEJ/B,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe8C,iBAAnD,CAFG,KAGE,IAAIhC,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAe+C,iBAAnD,IAAwE,KAH9E,CAAT;AAIH;AACJ,WA9CD,MA8CO;AACH9B,YAAAA,MAAM,GAAG,CAAE,CAACA,MAAM,GAAGR,QAAT,GAAoBK,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAegD,mBAAnD,CAArB,KACA,IAAIlC,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAeiD,mBAAnD,IAA0E,KAD9E,CAAD,GAEJnC,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAekD,kBAAnD,CAFG,KAGE,IAAIpC,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAemD,kBAAnD,IAAyE,KAH/E,CAAT;AAIH,WA5DiH,CA8DlH;;;AACA,cAAIC,uBAAuB,GAAG1C,SAAS,GAAG;AAAA;AAAA,gDAAe2C,2BAAlB,GAAgD;AAAA;AAAA,gDAAeC,0BAAtG;AACA,cAAIC,uBAAuB,GAAG7C,SAAS,GAAG;AAAA;AAAA,gDAAe8C,2BAAlB,GAAgD;AAAA;AAAA,gDAAeC,0BAAtG;AACA,cAAIC,sBAAsB,GAAIhD,SAAS,GAAG;AAAA;AAAA,gDAAeiD,0BAAlB,GAAgD;AAAA;AAAA,gDAAeC,yBAAtG;AACA,cAAIC,sBAAsB,GAAInD,SAAS,GAAG;AAAA;AAAA,gDAAeoD,0BAAlB,GAAgD;AAAA;AAAA,gDAAeC,yBAAtG;AAEA,cAAIC,cAAc,GAAGhD,YAAY,CAACf,4BAAb,CACjBmD,uBADiB,EACQG,uBADR,EAEjBG,sBAFiB,EAEOG,sBAFP,CAArB,CApEkH,CAwElH;;AACA,cAAII,UAAU,GAAG,CAAC,IAAIjD,YAAY,CAAChB,sBAAb,CAAoC;AAAA;AAAA,gDAAekE,mBAAnD,CAAL,KACV,IAAIlD,YAAY,CAAChB,sBAAb,CAAoC;AAAA;AAAA,gDAAemE,kBAAnD,CADM,CAAjB,CAzEkH,CA4ElH;;AACA,cAAIC,SAAJ;;AACA,cAAIxD,MAAJ,EAAY;AACRwD,YAAAA,SAAS,GAAG,CAAC,IAAItD,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAeqE,gBAAnD,CAAL,KACL,IAAIvD,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAesE,eAAnD,CADC,CAAZ;AAEH,WAHD,MAGO;AACHF,YAAAA,SAAS,GAAG,CAAC,IAAItD,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAeuE,kBAAnD,CAAL,KACL,IAAIzD,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAewE,iBAAnD,CADC,CAAZ;AAEH,WApFiH,CAsFlH;;;AACA,cAAIC,OAAJ;;AACA,cAAI,CAAC/D,SAAL,EAAgB;AACZ,gBAAIgE,mBAAmB,GAAG,CAA1B;AACA,gBAAIC,kBAAkB,GAAI,CAA1B;;AACA,oBAAOhE,UAAP;AACI,mBAAK;AAAA;AAAA,4CAAWY,SAAhB;AACImD,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeE,yBAArC;AACAD,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeE,wBAArC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWjD,MAAhB;AACI8C,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeI,sBAArC;AACAH,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeI,qBAArC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAW9C,SAAhB;AACIyC,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeM,yBAArC;AACAL,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeM,wBAArC;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAW3C,QAAhB;AACIoC,gBAAAA,mBAAmB,GAAG;AAAA;AAAA,sDAAeQ,uBAArC;AACAP,gBAAAA,kBAAkB,GAAI;AAAA;AAAA,sDAAeQ,sBAArC;AACA;AAhBR;;AAkBA,gBAAIxE,UAAU,IAAI;AAAA;AAAA,0CAAWgC,GAA7B,EAAkC;AAC9B8B,cAAAA,OAAO,GAAG,CAAC,IAAI3D,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAeoF,gBAAnD,CAAJ,GAA2EtE,YAAY,CAACd,sBAAb,CAAoC0E,mBAApC,CAA5E,KACH,IAAI5D,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAeqF,eAAnD,CAAJ,GAA2EvE,YAAY,CAACd,sBAAb,CAAoC2E,kBAApC,CADxE,CAAV;AAEH,aAHD,MAGO;AACHF,cAAAA,OAAO,GAAG,CAAC,IAAI3D,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAeoF,gBAAnD,CAAL,KACH,IAAItE,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,oDAAeqF,eAAnD,CADD,CAAV;AAEH;AACJ,WA5BD,MA4BO;AACHZ,YAAAA,OAAO,GAAG,CAAC,IAAI3D,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAesF,iBAAnD,CAAL,KACH,IAAIxE,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAeuF,gBAAnD,CADD,CAAV;AAEH,WAvHiH,CAyHlH;;;AACA,cAAIC,MAAM,GAAG,CAAEvE,MAAM,GAAGwD,OAAT,GAAmB5D,cAApB,GAAsCmD,cAAvC,IAAyDC,UAAzD,GAAsEG,SAAnF,CA1HkH,CA4HlH;;AACA,cAAI,CAAC1D,SAAD,IAAcF,QAAQ,CAACiF,QAAT,CAAkBC,OAAlB,CAA0B;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,YAAlB,CAA+BC,2BAAzD,CAAlB,EAAyG;AACrG,kBAAMC,WAAW,GAAG,CAAC,IAAEhF,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAe+F,oBAAnD,CAAH,KACb,IAAEjF,YAAY,CAACd,sBAAb,CAAoC;AAAA;AAAA,kDAAegG,mBAAnD,CADW,CAApB;AAEAR,YAAAA,MAAM,IAAIM,WAAV;AACH;;AAED,iBAAOG,IAAI,CAACC,IAAL,CAAUV,MAAV,CAAP;AACH;;AAEDW,QAAAA,QAAQ,GAAU;AACd,iBAAOF,IAAI,CAACG,KAAL,CAAW,KAAKnG,4BAAL,CAAkC;AAAA;AAAA,gDAAeoG,WAAjD,EAA8D;AAAA;AAAA,gDAAeC,WAA7E,EACd;AAAA;AAAA,gDAAeC,UADD,EACa;AAAA;AAAA,gDAAeC,UAD5B,CAAX,CAAP;AAEH;;AAEDtF,QAAAA,SAAS,GAAU;AACf,iBAAO+E,IAAI,CAACG,KAAL,CAAW,KAAKnG,4BAAL,CACd;AAAA;AAAA,gDAAewG,YADD,EACe;AAAA;AAAA,gDAAeC,YAD9B,EAEd;AAAA;AAAA,gDAAeC,WAFD,EAEc;AAAA;AAAA,gDAAeC,WAF7B,CAAX,CAAP;AAGH;;AAEDC,QAAAA,aAAa,GAAU;AACnB,iBAAOZ,IAAI,CAACG,KAAL,CAAW,KAAKnG,4BAAL,CACV;AAAA;AAAA,gDAAe6G,gBADL,EACuB;AAAA;AAAA,gDAAeC,gBADtC,EAEV;AAAA;AAAA,gDAAeC,eAFL,EAEsB;AAAA;AAAA,gDAAeC,eAFrC,IAGZ,KAAKd,QAAL,MACK,KAAKnG,sBAAL,CAA4B;AAAA;AAAA,gDAAekH,oBAA3C,IACI,KAAKlH,sBAAL,CAA4B;AAAA;AAAA,gDAAemH,mBAA3C,CAFT,IAE2E,KAL1E,CAAP;AAMH;;AA/NsB,O", "sourcesContent": ["import { MyApp } from \"../../app/MyApp\";\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { DamageType } from \"../../autogen/luban/schema\";\r\nimport type PlaneBase from \"../../game/ui/plane/PlaneBase\";\r\n\r\nexport class AttributeData {\r\n    private _baseAttributes: Map<AttributeConst, number> = new Map(); // 基础属性\r\n    private _Attributes: Map<AttributeConst, number> = new Map(); // 缓存最终属性值\r\n    private _AttributeModifies: Map<AttributeConst, Map<number, number>> = new Map();\r\n    private _sources: Map<number,  Set<AttributeConst>> = new Map()// 属性来源\r\n\r\n    constructor() {\r\n    }\r\n\r\n    addBaseAttribute(key: AttributeConst, value: number) {\r\n        this._baseAttributes.set(key, this._baseAttributes.get(key)||0 + value);\r\n        this.recalculateAttributes(key)\r\n    }\r\n\r\n    // 添加属性来源\r\n    addModify(id: number, key:AttributeConst, value: number) {\r\n        let modify = this._AttributeModifies.get(key);\r\n        if (!modify) {\r\n            modify = new Map()\r\n            this._AttributeModifies.set(key, modify)\r\n        }\r\n        modify.set(id, value)\r\n\r\n        let source = this._sources.get(id);\r\n        if (!source) {\r\n            source = new Set()\r\n            this._sources.set(id, source);\r\n        }\r\n        source.add(key);\r\n\r\n        this.recalculateAttributes(key);\r\n    }\r\n\r\n    // 移除属性来源\r\n    removeModify(id: number) {\r\n        let source = this._sources.get(id)\r\n        source?.forEach((key) => {\r\n            let modify = this._AttributeModifies.get(key);\r\n            if (modify) {\r\n                modify.delete(id);\r\n                this.recalculateAttributes(key);\r\n            }\r\n        });\r\n        this._sources.delete(id);\r\n    }\r\n\r\n    // 重新计算属性\r\n    private recalculateAttributes(key:AttributeConst) {\r\n        let finalValue = this._baseAttributes.get(key) || 0;\r\n\r\n        // 遍历所有属性来源\r\n        this._AttributeModifies.get(key)?.forEach(add => {\r\n            finalValue += add;\r\n        });\r\n\r\n        // 计算最终属性值: (基础值 + 加法值) * (1 + 百分比值/10000)\r\n        this._Attributes.set(key, finalValue);\r\n    }\r\n\r\n    getFinalAttributeByKey(key:AttributeConst):number {\r\n        return this._Attributes.get(key) || 0\r\n    }\r\n\r\n    getFinialAttributeByOutInKey(outAddKey:AttributeConst, outPreKey:AttributeConst, \r\n        inAddKey:AttributeConst, inPerKey:AttributeConst): number {\r\n\r\n        return (this.getFinalAttributeByKey(outAddKey) * (1 + this.getFinalAttributeByKey(outPreKey) / 10000) \r\n            + this.getFinalAttributeByKey(inAddKey)) * (1 + this.getFinalAttributeByKey(inPerKey) / 10000)\r\n    }\r\n\r\n    static CalcBulletDamage(attacker: PlaneBase, defender: PlaneBase, \r\n            hurtRate: number, isNuclear: boolean, damageType: DamageType, isBoss: boolean, attackPowerFix:number): number {\r\n\r\n        const attackerAttr = attacker.attribute;\r\n        const defenderAttr = defender.attribute;\r\n        // ①局外属性面板上显示的攻击力值=(攻击力局外绝对值1+攻击力局外绝对值2+…)×(1+攻击力局外百分比1+攻击力局外百分比2+…)\r\n        // ②攻击力局内总值=(①局外属性面板上显示的攻击力值+攻击力局内绝对值1+攻击力局内绝对值2+⋯)×(1+攻击力局内百分比1+攻击力局内百分比2+⋯)\r\n        let attack = attackerAttr.getAttack();\r\n\r\n        // ③子弹or核弹的局内攻击力=[(②攻击力局内总值×攻击转换系数+子弹攻击局外绝对值1+子弹攻击局外绝对值2+⋯)×(1+子弹攻击局外百分比1+子弹攻击局外百分比2+⋯)+子弹攻击局内绝对值1+子弹攻击局内绝对值2+⋯]×(1+子弹攻击局内百分比1+子弹攻击局内百分比2+⋯)\r\n        if (!isNuclear) {\r\n            let damageAttackOutAddKey = 0;;\r\n            let damageAttackOutPerKey = 0;;\r\n            let damageAttackInAddKey  = 0;;\r\n            let damageAttackInPerKey  = 0;;\r\n            switch(damageType) {\r\n                case DamageType.EXPLOSIVE:\r\n                    damageAttackOutAddKey = AttributeConst.ExplosiveBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.ExplosiveBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.ExplosiveBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.ExplosiveBulletAttackOutPer\r\n                    break;\r\n                case DamageType.NORMAL:\r\n                    damageAttackOutAddKey = AttributeConst.NormalBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.NormalBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.NormalBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.NormalBulletAttackOutPer\r\n                    break;\r\n                case DamageType.ENERGETIC:\r\n                    damageAttackOutAddKey = AttributeConst.EnergeticBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.EnergeticBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.EnergeticBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.EnergeticBulletAttackOutPer\r\n                    break;\r\n                case DamageType.PHYSICAL:\r\n                    damageAttackOutAddKey = AttributeConst.PhysicsBulletAttackInAdd\r\n                    damageAttackOutPerKey = AttributeConst.PhysicsBulletAttackInPer\r\n                    damageAttackInAddKey  = AttributeConst.PhysicsBulletAttackOutAdd\r\n                    damageAttackInPerKey  = AttributeConst.PhysicsBulletAttackOutPer\r\n                    break;\r\n            }\r\n            if (damageType != DamageType.ALL) {\r\n                attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutAdd)\r\n                            + attackerAttr.getFinalAttributeByKey(damageAttackOutAddKey))\r\n                        * (1 + (attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutPer)\r\n                            + attackerAttr.getFinalAttributeByKey(damageAttackOutPerKey)) / 10000))\r\n                    + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInAdd)\r\n                    + attackerAttr.getFinalAttributeByKey(damageAttackInAddKey)) \r\n                    * (1 + (attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInPer) \r\n                        + attackerAttr.getFinalAttributeByKey(damageAttackInPerKey)) / 10000);\r\n            } else {\r\n                attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutAdd))\r\n                        * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutPer) / 10000))\r\n                    + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInAdd)) \r\n                        * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInPer) / 10000);\r\n            }\r\n        } else {\r\n            attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackOutAdd))\r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackOutPer) / 10000))\r\n                + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackInAdd)) \r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackInPer) / 10000);\r\n        }\r\n\r\n        // ④子弹or核弹伤害抗性局内总值=[(子弹伤害抗性局外绝对值1+子弹伤害抗性局外绝对值2+⋯)×(1+子弹伤害抗性局外百分比1+子弹伤害抗性局外百分比2+⋯)+子弹伤害抗性局内绝对值1+子弹伤害抗性局内绝对值2+⋯]×(1+子弹伤害抗性局内百分比1+子弹伤害抗性局内百分比2+⋯)\r\n        let hurtResistanceOutAddKey = isNuclear ? AttributeConst.NuclearHurtResistanceOutAdd : AttributeConst.BulletHurtResistanceOutAdd;\r\n        let hurtResistanceOutPerKey = isNuclear ? AttributeConst.NuclearHurtResistanceOutPer : AttributeConst.BulletHurtResistanceOutPer; \r\n        let hurtResistanceInAddKey  = isNuclear ? AttributeConst.NuclearHurtResistanceInAdd  : AttributeConst.BulletHurtResistanceInAdd;\r\n        let hurtResistanceInPerKey  = isNuclear ? AttributeConst.NuclearHurtResistanceInPer  : AttributeConst.BulletHurtResistanceInPer;  \r\n\r\n        let hurtResistance = defenderAttr.getFinialAttributeByOutInKey(\r\n            hurtResistanceOutAddKey, hurtResistanceOutPerKey,\r\n            hurtResistanceInAddKey, hurtResistanceInPerKey);\r\n\r\n        // ⑤承受子弹伤害%局内总值=(1-子弹伤害减免局外百分比1-子弹伤害减免局外百分比2-…)×(1-子弹伤害减免局内百分比1-子弹伤害减免局内百分比2-…)\r\n        let hurtDerate = (1 - defenderAttr.getFinalAttributeByKey(AttributeConst.BulletHurtDerateOut)) \r\n            * (1 - defenderAttr.getFinalAttributeByKey(AttributeConst.BulletHurtDerateIn));\r\n\r\n        // ⑥对boss or 普通怪物伤害%局内总值=(1+对boss or 普通怪物伤害局外百分比1+对boss or 普通怪物伤害局外百分比2+⋯)×(1+对boss or 普通怪物伤害局内百分比1+对boss or 普通怪物伤害局内百分比2+⋯)\r\n        let hurtBonus: number\r\n        if (isBoss) {\r\n            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BossHurtBonusOut)) \r\n                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BossHurtBonusIn));\r\n        } else {\r\n            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NormalHurtBonusOut)) \r\n                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NormalHurtBonusIn));\r\n        }\r\n\r\n        //⑦子弹（分类型）or核弹的局内伤害修正=(1+子弹伤害局外百分比1+子弹伤害局外百分比2+⋯)×(1+子弹伤害局内百分比1+子弹伤害局内百分比2+⋯)\r\n        let hurtFix:number\r\n        if (!isNuclear) {\r\n            let damageHurtFixOutKey = 0;\r\n            let damageHurtFixInKey  = 0;\r\n            switch(damageType) {\r\n                case DamageType.EXPLOSIVE:\r\n                    damageHurtFixOutKey = AttributeConst.ExplosiveBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.ExplosiveBulletHurtFixIn\r\n                    break;\r\n                case DamageType.NORMAL:\r\n                    damageHurtFixOutKey = AttributeConst.NormalBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.NormalBulletHurtFixIn\r\n                    break;\r\n                case DamageType.ENERGETIC:\r\n                    damageHurtFixOutKey = AttributeConst.EnergeticBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.EnergeticBulletHurtFixIn\r\n                    break;\r\n                case DamageType.PHYSICAL:\r\n                    damageHurtFixOutKey = AttributeConst.PhysicsBulletHurtFixOut\r\n                    damageHurtFixInKey  = AttributeConst.PhysicsBulletHurtFixIn\r\n                    break;\r\n            }\r\n            if (damageType != DamageType.ALL) {\r\n                hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixOut) + attackerAttr.getFinalAttributeByKey(damageHurtFixOutKey)) \r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixIn) +  attackerAttr.getFinalAttributeByKey(damageHurtFixInKey));\r\n            } else {\r\n                hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixOut)) \r\n                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixIn));\r\n            }\r\n        } else {\r\n            hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearHurtFixOut)) \r\n                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearHurtFixIn));\r\n        }\r\n\r\n        // ⑧子弹or核弹最终造成的伤害=[(③子弹or核弹的局内攻击力×⑦子弹or核弹的局内伤害修正×战斗力差伤害修正百分比)-④受击方子弹伤害抗性局内总值]×⑤受击方承受子弹伤害%局内总值×(1+⑥攻击方对boss or 普通怪物伤害%局内总值)\r\n        let damage = ((attack * hurtFix * attackPowerFix) - hurtResistance) * hurtDerate * hurtBonus;\r\n\r\n        // ○15弹的局内破甲伤害（对核弹和主动技能无效）=(1+子弹破甲伤害局外百分比1+子弹破甲伤害局外百分比2+⋯)×(1+子弹破甲伤害局内百分比1+子弹破甲伤害局内百分比2+⋯)\r\n        if (!isNuclear && defender.buffComp.HasBuff(MyApp.lubanTables.TbGlobalAttr.BulletPenetrationFlagBuffID)) {\r\n            const penetration = (1+attackerAttr.getFinalAttributeByKey(AttributeConst.BulletPenetrationOut))\r\n                * (1+attackerAttr.getFinalAttributeByKey(AttributeConst.BulletPenetrationIn));\r\n            damage *= penetration;\r\n        }\r\n\r\n        return Math.ceil(damage);\r\n    }\r\n\r\n    getMaxHP():number {\r\n        return Math.floor(this.getFinialAttributeByOutInKey(AttributeConst.MaxHPOutAdd, AttributeConst.MaxHPOutPer, \r\n            AttributeConst.MaxHPInAdd, AttributeConst.MaxHPInPer));\r\n    }\r\n\r\n    getAttack():number {\r\n        return Math.floor(this.getFinialAttributeByOutInKey(\r\n            AttributeConst.AttackOutAdd, AttributeConst.AttackOutPer, \r\n            AttributeConst.AttackInAdd, AttributeConst.AttackInPer));\r\n    }\r\n\r\n    getHPRecovery():number {\r\n        return Math.floor(this.getFinialAttributeByOutInKey(\r\n                AttributeConst.HPRecoveryOutAdd, AttributeConst.HPRecoveryOutPer, \r\n                AttributeConst.HPRecoveryInAdd, AttributeConst.HPRecoveryInPer)\r\n            + this.getMaxHP() \r\n                * (this.getFinalAttributeByKey(AttributeConst.MaxHPRecoveryRateOut) \r\n                    + (this.getFinalAttributeByKey(AttributeConst.MaxHPRecoveryRateIn)))/10000);\r\n    }\r\n    \r\n}"]}